{"instruction_codes": {"N": "跟近点", "F": "跟远点", "S": "找东西", "P": "停止", "C": "切换跟随对象"}, "examples_by_category": {"basic": [{"id": "1", "input": "跟紧点", "output": "N"}, {"id": "2", "input": "跟远点", "output": "F"}, {"id": "3", "input": "找东西", "output": "S"}, {"id": "4", "input": "停下来", "output": "P"}, {"id": "5", "input": "切换跟随对象", "output": "C"}], "compound": [{"id": "6", "input": "跟紧点然后跟远点", "output": "N-F"}, {"id": "7", "input": "找东西，找到后跟近一点", "output": "S-N"}, {"id": "8", "input": "停下来观察一下，然后继续找东西", "output": "P-S"}, {"id": "9", "input": "切换跟随对象后跟远一点", "output": "C-F"}, {"id": "10", "input": "跟近点跟近点", "output": "N-N"}], "complex": [{"id": "11", "input": "跟紧点，找找东西，然后停下来", "output": "N-S-P"}, {"id": "12", "input": "先跟近一点，然后跟近一点，再跟近一点", "output": "N-N-N"}, {"id": "13", "input": "跟远点找东西，找完后停下来，然后切换跟随对象", "output": "F-S-P-C"}, {"id": "14", "input": "切换跟随对象后跟近点，再切换跟随对象后跟近点", "output": "C-N-C-N"}, {"id": "15", "input": "找东西，找到后跟远点，然后再找一次", "output": "S-F-S"}, {"id": "16", "input": "跟远点，停下来找东西，找完再跟近点", "output": "F-P-S-N"}, {"id": "17", "input": "切换跟随对象后跟远点，再切换后跟近点，最后停下来", "output": "C-F-C-N-P"}, {"id": "18", "input": "找东西，停下来后切换跟随对象，再找一次停下来", "output": "S-P-C-S-P"}], "negative": [{"id": "19", "input": "不要跟太近", "output": "F"}, {"id": "20", "input": "别跟那么紧", "output": "F"}, {"id": "21", "input": "不要再找了", "output": "P"}, {"id": "22", "input": "不要跟太远，跟近一点", "output": "N"}], "correction": [{"id": "24", "input": "太远了，往前一点", "output": "N"}, {"id": "25", "input": "太近了，退后一点", "output": "F"}, {"id": "26", "input": "跟错人了，切换一下", "output": "C"}, {"id": "27", "input": "找过头了，停下来重新找", "output": "P-S"}, {"id": "28", "input": "跟得太近了，往后退一点，然后继续找", "output": "F-S"}], "edge_cases": [{"id": "29", "input": "停一下，不对，还是继续找吧", "output": "P-S"}, {"id": "30", "input": "切换目标，不对，换回来", "output": "C-C"}, {"id": "31", "input": "跟近点，算了，还是跟远点吧", "output": "N-F"}, {"id": "32", "input": "停下，等等，再停一下，好了继续找", "output": "P-P-S"}, {"id": "33", "input": "跟近点，再近点，不对太近了，往后退", "output": "N-N-F"}], "irrelevant": [{"id": "34", "input": "你好", "output": ""}, {"id": "35", "input": "早上好", "output": ""}, {"id": "36", "input": "谢谢", "output": ""}, {"id": "37", "input": "再见", "output": ""}, {"id": "38", "input": "今天天气真好", "output": ""}, {"id": "39", "input": "向前走", "output": ""}]}}