#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
服务器启动脚本

此脚本提供了一个便捷的方式来启动Web服务框架，
包括环境检查、配置验证和服务启动。
"""

import os
import sys
import logging
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version.split()[0]}")
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'fastapi',
        'uvicorn',
        'websockets',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_directories():
    """检查并创建必要的目录"""
    directories = ['data', 'logs', 'temp', 'models', 'ssl']
    
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"✅ 目录已存在: {directory}")

def check_config():
    """检查配置文件"""
    config_files = [
        ('config.py', '配置文件'),
        ('.env.template', '环境配置模板'),
        ('main.py', '主服务文件'),
        ('service_template.py', '服务模板文件')
    ]
    
    for file_path, description in config_files:
        if os.path.exists(file_path):
            print(f"✅ {description}: {file_path}")
        else:
            print(f"❌ 缺少{description}: {file_path}")
            return False
    
    # 检查.env文件
    if not os.path.exists('.env'):
        print("⚠️  警告: 未找到.env文件")
        print("建议复制.env.template为.env并根据需要修改配置")
    else:
        print("✅ 环境配置文件: .env")
    
    return True

def check_ssl():
    """检查SSL证书"""
    ssl_key = "ssl/key.pem"
    ssl_cert = "ssl/cert.pem"
    
    if os.path.exists(ssl_key) and os.path.exists(ssl_cert):
        print("✅ SSL证书文件已存在")
        return True
    else:
        print("⚠️  警告: 未找到SSL证书文件")
        print("服务将以HTTP模式启动")
        print("如需HTTPS，请生成SSL证书或将证书文件放置在ssl/目录下")
        return False

def start_server():
    """启动服务器"""
    print("\n" + "="*60)
    print("🚀 启动通用Web服务框架...")
    print("="*60)
    
    try:
        # 导入并启动主服务
        import main
        print("服务器启动成功！")
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔍 通用Web服务框架 - 启动检查")
    print("="*60)
    
    # 环境检查
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("目录结构", check_directories),
        ("配置文件", check_config),
        ("SSL证书", check_ssl)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        print(f"\n📋 检查{check_name}...")
        if not check_func():
            all_passed = False
    
    print("\n" + "="*60)
    
    if not all_passed:
        print("❌ 环境检查未通过，请解决上述问题后重试")
        return False
    
    print("✅ 所有检查通过！")
    
    # 询问是否启动服务器
    try:
        response = input("\n是否现在启动服务器? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是', '']:
            return start_server()
        else:
            print("👋 稍后可以运行 'python main.py' 启动服务器")
            return True
    except KeyboardInterrupt:
        print("\n👋 已取消启动")
        return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
