#!/bin/bash
# Git仓库初始化脚本

echo "正在初始化新的Git仓库..."

# 初始化Git仓库
git init

# 创建.gitignore文件
cat > .gitignore << 'EOF'
# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定文件
data/
logs/
temp/
models/*.bin
models/*.pt
models/*.pth

# SSL证书（敏感信息）
ssl/*.pem
ssl/*.key
ssl/*.crt

# 临时文件
*.tmp
*.temp
*.log

# Node.js (如果有前端构建)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
EOF

# 添加所有文件到暂存区
git add .

# 创建初始提交
git commit -m "Initial commit: 通用Web服务框架模板

- 基于FastAPI的Web服务框架
- 支持WebSocket实时通信
- 模块化服务架构
- 完整的配置管理系统
- 内置监控功能
- 支持多种服务类型扩展"

echo "Git仓库初始化完成！"
echo ""
echo "下一步操作："
echo "1. 添加远程仓库: git remote add origin <your-repo-url>"
echo "2. 推送到远程仓库: git push -u origin main"
echo ""
echo "或者继续本地开发："
echo "1. 查看状态: git status"
echo "2. 添加更改: git add ."
echo "3. 提交更改: git commit -m 'your message'"
