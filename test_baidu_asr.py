#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度ASR服务测试脚本

用于测试百度实时语音识别服务的基本功能
"""

import asyncio
import logging
import sys
import os

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_baidu_asr_service():
    """测试百度ASR服务"""
    try:
        # 导入服务
        from baidu_asr_service import BaiduASRService
        
        logger.info("开始测试百度ASR服务...")
        
        # 创建服务实例
        asr_service = BaiduASRService()
        
        # 初始化服务
        logger.info("初始化服务...")
        init_result = await asr_service.initialize()
        
        if not init_result:
            logger.error("服务初始化失败")
            return False
        
        logger.info("服务初始化成功")
        
        # 获取服务状态
        status = asr_service.get_status()
        logger.info(f"服务状态: {status}")
        
        # 测试配置
        logger.info(f"APPID: {asr_service.appid}")
        logger.info(f"DEV_PID: {asr_service.dev_pid}")
        logger.info(f"URI: {asr_service.uri}")
        
        logger.info("百度ASR服务测试完成")
        return True
        
    except ImportError as e:
        logger.error(f"导入失败: {e}")
        logger.error("请确保已安装websocket-client: pip install websocket-client")
        return False
    except Exception as e:
        logger.error(f"测试失败: {e}")
        return False

async def test_websocket_connection():
    """测试WebSocket连接"""
    try:
        import websocket
        import const
        import uuid
        
        logger.info("测试WebSocket连接...")
        
        # 创建测试URI
        uri = f"{const.URI}?sn={str(uuid.uuid4())}"
        logger.info(f"连接URI: {uri}")
        
        # 测试连接（不实际发送数据）
        def on_open(ws):
            logger.info("WebSocket连接成功")
            ws.close()
        
        def on_error(ws, error):
            logger.error(f"WebSocket连接错误: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            logger.info("WebSocket连接已关闭")
        
        # 创建WebSocket连接
        ws = websocket.WebSocketApp(
            uri,
            on_open=on_open,
            on_error=on_error,
            on_close=on_close
        )
        
        # 设置超时
        import threading
        import time
        
        def timeout_handler():
            time.sleep(5)  # 5秒超时
            ws.close()
        
        timeout_thread = threading.Thread(target=timeout_handler)
        timeout_thread.daemon = True
        timeout_thread.start()
        
        # 运行连接测试
        ws.run_forever()
        
        logger.info("WebSocket连接测试完成")
        return True
        
    except Exception as e:
        logger.error(f"WebSocket连接测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖"""
    logger.info("检查依赖包...")
    
    required_packages = [
        ('websocket', 'websocket-client'),
        ('const', '本地const.py文件')
    ]
    
    missing = []
    
    for package, description in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {description}")
        except ImportError:
            logger.error(f"✗ {description}")
            missing.append(description)
    
    if missing:
        logger.error(f"缺少依赖: {', '.join(missing)}")
        return False
    
    logger.info("所有依赖检查通过")
    return True

def check_config():
    """检查配置"""
    logger.info("检查配置...")
    
    try:
        import const
        
        config_items = [
            ('APPID', const.APPID),
            ('APPKEY', const.APPKEY),
            ('DEV_PID', const.DEV_PID),
            ('URI', const.URI)
        ]
        
        for name, value in config_items:
            if value:
                logger.info(f"✓ {name}: {value}")
            else:
                logger.error(f"✗ {name}: 未配置")
                return False
        
        logger.info("配置检查通过")
        return True
        
    except Exception as e:
        logger.error(f"配置检查失败: {e}")
        return False

async def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("百度ASR服务测试")
    logger.info("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        logger.error("依赖检查失败，请安装缺少的依赖包")
        return False
    
    # 检查配置
    if not check_config():
        logger.error("配置检查失败，请检查const.py文件")
        return False
    
    # 测试服务
    service_test = await test_baidu_asr_service()
    if not service_test:
        logger.error("服务测试失败")
        return False
    
    # 测试WebSocket连接
    connection_test = await test_websocket_connection()
    if not connection_test:
        logger.error("WebSocket连接测试失败")
        return False
    
    logger.info("=" * 60)
    logger.info("所有测试通过！百度ASR服务可以正常使用")
    logger.info("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if not result:
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        sys.exit(1)
