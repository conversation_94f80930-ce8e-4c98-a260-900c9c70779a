<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音识别监控</title>
    <style>
        :root {
            --primary: #3a86ff;
            --secondary: #00f5d4;
            --accent: #8338ec;
            --dark: #0f0e17;
            --text: #fffffe;
            --panel: rgba(15, 23, 42, 0.7);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--dark);
            color: var(--text);
            min-height: 100vh;
            overflow-x: hidden;
            background-image: 
                radial-gradient(circle at 20% 35%, rgba(58, 134, 255, 0.15) 0%, transparent 29%),
                radial-gradient(circle at 70% 65%, rgba(131, 56, 236, 0.1) 0%, transparent 20%);
        }

        .container {
            max-width: 1200px;
            padding: 20px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        header {
            text-align: center;
            padding: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .title {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: relative;
            display: inline-block;
            letter-spacing: 1px;
        }

        .home-btn {
            padding: 8px 16px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .home-btn:hover {
            box-shadow: 0 4px 12px rgba(131, 56, 236, 0.3);
            transform: translateY(-2px);
        }

        .monitor-panel {
            background: var(--panel);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            flex: 1;
            overflow-y: auto;
            position: relative;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            background: rgba(15, 14, 23, 0.5);
            padding: 10px 15px;
            border-radius: 10px;
        }

        .status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #45a29e;
        }

        .status-dot.offline {
            background: #f44336;
        }

        .clear-btn {
            padding: 5px 10px;
            background: rgba(131, 56, 236, 0.2);
            border: 1px solid rgba(131, 56, 236, 0.3);
            color: var(--text);
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .clear-btn:hover {
            background: rgba(131, 56, 236, 0.3);
        }

        .records-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .record-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 16px;
            animation: fadeIn 0.5s ease;
            position: relative;
            border-left: 3px solid var(--secondary);
        }

        .record-time {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
        }

        .record-text {
            margin-top: 10px;
            font-size: 1rem;
        }

        .no-records {
            text-align: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.5);
            font-style: italic;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* 自定义滚动条 */
        .monitor-panel::-webkit-scrollbar {
            width: 6px;
        }

        .monitor-panel::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            margin: 10px 0;
        }

        .monitor-panel::-webkit-scrollbar-thumb {
            background: rgba(131, 56, 236, 0.4);
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .monitor-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(131, 56, 236, 0.6);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .title {
                font-size: 1.4rem;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 10px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1 class="title">语音识别监控面板</h1>
            <a href="/" class="home-btn">返回主页</a>
        </header>
        
        <div class="monitor-panel">
            <div class="status-bar">
                <div class="status">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">正在连接...</span>
                </div>
                <button class="clear-btn" id="clearBtn">清空记录</button>
            </div>
            
            <div class="records-container" id="recordsContainer">
                <div class="no-records" id="noRecords">暂无识别记录</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');
        const recordsContainer = document.getElementById('recordsContainer');
        const noRecords = document.getElementById('noRecords');
        const clearBtn = document.getElementById('clearBtn');
        
        // 初始化WebSocket连接
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            if (ws === null || ws.readyState === WebSocket.CLOSED) {
                ws = new WebSocket(`${protocol}//${window.location.host}/monitor_ws`);
                
                ws.onopen = function() {
                    statusDot.classList.remove('offline');
                    statusText.textContent = '已连接，等待数据...';
                    console.log("监控WebSocket连接已建立");
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    addRecord(data.text, data.timestamp || new Date().toISOString());
                    statusText.textContent = '已连接，最后更新: ' + new Date().toLocaleTimeString();
                };

                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                    statusDot.classList.add('offline');
                    statusText.textContent = '连接错误，请刷新页面';
                };
                
                ws.onclose = function() {
                    console.log("WebSocket连接已关闭");
                    statusDot.classList.add('offline');
                    statusText.textContent = '连接已断开，正在重连...';
                    // 尝试重新连接
                    setTimeout(function() {
                        initWebSocket();
                    }, 2000);
                };
            }
        }

        // 添加识别记录
        function addRecord(text, timestamp) {
            if (noRecords.style.display !== 'none') {
                noRecords.style.display = 'none';
            }
            
            const date = new Date(timestamp);
            const formattedTime = date.toLocaleTimeString() + '.' + date.getMilliseconds().toString().padStart(3, '0');
            
            const recordItem = document.createElement('div');
            recordItem.className = 'record-item';
            recordItem.innerHTML = `
                <div class="record-time">${formattedTime}</div>
                <div class="record-text">${text}</div>
            `;
            
            recordsContainer.insertBefore(recordItem, recordsContainer.firstChild);
        }

        // 清空记录
        clearBtn.addEventListener('click', function() {
            recordsContainer.innerHTML = '';
            noRecords.style.display = 'block';
            recordsContainer.appendChild(noRecords);
        });

        // 页面加载时初始化WebSocket
        window.onload = function() {
            initWebSocket();
        };

        // 页面关闭前关闭WebSocket
        window.onbeforeunload = function() {
            if (ws) {
                ws.close();
            }
        };
    </script>
</body>
</html> 