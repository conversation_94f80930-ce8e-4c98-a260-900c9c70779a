#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FunASR环境设置脚本

此脚本用于在conda环境asr-llm中安装和配置FunASR语音识别服务。

使用方法：
1. 激活conda环境: conda activate asr-llm
2. 运行此脚本: python setup_funasr.py
3. 启动服务: python main.py
"""

import subprocess
import sys
import os
import logging

# 设置输出编码为UTF-8，解决Windows编码问题
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def run_command(command, description):
    """
    执行命令并显示进度
    """
    logging.info(f"正在{description}...")
    logging.info(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        logging.info(f"[成功] {description}成功")
        return True
    except subprocess.CalledProcessError as e:
        logging.error(f"[失败] {description}失败")
        logging.error(f"错误输出: {e.stderr}")
        return False

def check_conda_env():
    """
    检查是否在正确的conda环境中
    """
    conda_env = os.environ.get('CONDA_DEFAULT_ENV', '')
    if conda_env != 'asr-llm':
        logging.warning(f"[警告] 当前环境: {conda_env}")
        logging.warning("建议在asr-llm环境中运行此脚本")
        logging.info("请运行: conda activate asr-llm")
        return False

    logging.info(f"[成功] 当前环境: {conda_env}")
    return True

def install_dependencies():
    """
    安装FunASR依赖包
    """
    logging.info("[安装] 开始安装FunASR依赖...")
    
    # 基础依赖
    dependencies = [
        "pip install funasr==1.0.23",
        "pip install torch>=1.13.0 torchaudio>=0.13.0 --index-url https://download.pytorch.org/whl/cpu",
        "pip install numpy soundfile librosa",
        "pip install modelscope",  # 用于下载模型
    ]
    
    for dep in dependencies:
        if not run_command(dep, f"安装 {dep.split()[-1]}"):
            return False
    
    return True

def download_models():
    """
    下载FunASR模型
    """
    logging.info("[下载] 开始下载FunASR模型...")

    # 创建模型下载脚本
    download_script = '''
import os
from funasr import AutoModel

try:
    print("正在下载SenseVoice模型...")
    model = AutoModel(
        model="iic/SenseVoiceSmall",
        vad_model="fsmn-vad",
        vad_kwargs={"max_single_segment_time": 30000},
        device="cpu"
    )
    print("[成功] 模型下载完成")

    # 测试模型
    print("[测试] 测试模型...")
    # 这里不实际测试，只是确保模型可以加载
    print("[成功] 模型测试通过")

except Exception as e:
    print(f"[失败] 模型下载失败: {e}")
    exit(1)
'''
    
    # 写入临时脚本文件
    with open('temp_download_models.py', 'w', encoding='utf-8') as f:
        f.write(download_script)
    
    try:
        # 执行下载脚本
        result = subprocess.run([sys.executable, 'temp_download_models.py'], 
                              capture_output=True, text=True, timeout=1800)  # 30分钟超时
        
        if result.returncode == 0:
            logging.info("[成功] 模型下载成功")
            logging.info(result.stdout)
            return True
        else:
            logging.error("[失败] 模型下载失败")
            logging.error(result.stderr)
            return False

    except subprocess.TimeoutExpired:
        logging.error("[失败] 模型下载超时（30分钟）")
        return False
    except Exception as e:
        logging.error(f"[失败] 模型下载异常: {e}")
        return False
    finally:
        # 清理临时文件
        if os.path.exists('temp_download_models.py'):
            os.remove('temp_download_models.py')

def test_funasr():
    """
    测试FunASR安装
    """
    logging.info("[测试] 测试FunASR安装...")

    test_script = '''
try:
    from funasr import AutoModel
    import torch
    import torchaudio
    import numpy as np
    import soundfile as sf

    print("[成功] 所有依赖包导入成功")

    # 检查PyTorch
    print(f"PyTorch版本: {torch.__version__}")
    print(f"设备支持: {'CUDA' if torch.cuda.is_available() else 'CPU'}")

    print("[成功] FunASR环境测试通过")

except ImportError as e:
    print(f"[失败] 依赖包导入失败: {e}")
    exit(1)
except Exception as e:
    print(f"[失败] 测试失败: {e}")
    exit(1)
'''
    
    # 写入临时测试脚本
    with open('temp_test_funasr.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    try:
        result = subprocess.run([sys.executable, 'temp_test_funasr.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            logging.info("[成功] FunASR测试通过")
            logging.info(result.stdout)
            return True
        else:
            logging.error("[失败] FunASR测试失败")
            logging.error(result.stderr)
            return False
            
    finally:
        # 清理临时文件
        if os.path.exists('temp_test_funasr.py'):
            os.remove('temp_test_funasr.py')

def main():
    """
    主安装流程
    """
    logging.info("=" * 60)
    logging.info("[开始] FunASR环境设置开始")
    logging.info("=" * 60)

    # 检查conda环境
    if not check_conda_env():
        logging.error("请先激活asr-llm环境: conda activate asr-llm")
        return False

    # 安装依赖
    if not install_dependencies():
        logging.error("依赖安装失败")
        return False

    # 测试安装
    if not test_funasr():
        logging.error("FunASR测试失败")
        return False

    # 下载模型（可选，首次运行时自动下载）
    logging.info("[信息] 模型将在首次使用时自动下载")
    logging.info("如需预先下载，请手动运行模型下载")

    logging.info("=" * 60)
    logging.info("[完成] FunASR环境设置完成！")
    logging.info("=" * 60)
    logging.info("现在可以启动服务:")
    logging.info("  python main.py")
    logging.info("")
    logging.info("首次启动时会自动下载模型，请耐心等待...")

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1) 