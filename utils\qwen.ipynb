{"cells": [{"cell_type": "code", "execution_count": 1, "id": "d5212c47", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好！很高兴能够与你聊天。有什么我可以帮助你的吗？你好！有什么我能帮助你的吗？\n"]}], "source": ["import openai\n", "openai.api_base = \"http://114.111.24.248:8901/v1\"\n", "openai.api_key = \"none\"\n", "\n", "# create a request activating streaming response\n", "for chunk in openai.ChatCompletion.create(\n", "    model=\"Qwen\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"你好\"}\n", "    ],\n", "    stream=True \n", "    # Specifying stop words in streaming output format is not yet supported and is under development.\n", "):\n", "    if hasattr(chunk.choices[0].delta, \"content\"):\n", "        print(chunk.choices[0].delta.content, end=\"\", flush=True)\n", "\n", "# create a request not activating streaming response\n", "response = openai.ChatCompletion.create(\n", "    model=\"Qwen\",\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": \"你好\"}\n", "    ],\n", "    stream=False,\n", "    stop=[] # You can add custom stop words here, e.g., stop=[\"Observation:\"] for ReAct prompting.\n", ")\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 4, "id": "bf0b4cf5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ChatCompletion(id=None, choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='你好！有什么我能帮到你的吗？', refusal=None, role='assistant', annotations=None, audio=None, function_call=None, tool_calls=None))], created=1742267934, model='qwen2.5:7b', object='chat.completion', service_tier=None, system_fingerprint=None, usage=None)\n"]}], "source": ["from openai import OpenAI\n", "client = OpenAI(\n", "    base_url='http://114.111.24.248:8901/v1/',\n", "    api_key='ollama',  # required but ignored\n", ")\n", "chat_completion = client.chat.completions.create(\n", "    messages=[\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"你是一个能够理解中英文指令并帮助完成任务的智能助手。你的任务是根据用户的需求生成合适的分类任务或生成任务，并准确判断这些任务的类型。请确保你的回答简洁、准确且符合中英文语境。用中文回答\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"你好\",\n", "        }\n", "    ],\n", "\n", "    #model='llama3.2:1b',\n", "    model='qwen2.5:7b',\n", "\n", "    max_tokens=38192,\n", "    temperature=0.7,\n", "    top_p=0.5,\n", "    frequency_penalty=0,\n", "    presence_penalty=2,\n", ")\n", "\n", "print(chat_completion)\n"]}, {"cell_type": "code", "execution_count": 6, "id": "08890616", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你好！有什么我能帮到你的吗？\n"]}], "source": ["from openai import OpenAI\n", "client = OpenAI(\n", "    base_url='http://114.111.24.248:8901/v1/',\n", "    api_key='ollama',  # required but ignored\n", ")\n", "chat_completion = client.chat.completions.create(\n", "    messages=[\n", "        {\n", "            \"role\": \"system\",\n", "            \"content\": \"你是一个能够理解中英文指令并帮助完成任务的智能助手。你的任务是根据用户的需求生成合适的分类任务或生成任务，并准确判断这些任务的类型。请确保你的回答简洁、准确且符合中英文语境。用中文回答\",\n", "        },\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"你好\",\n", "        }\n", "    ],\n", "\n", "    #model='llama3.2:1b',\n", "    model='qwen2.5:7b',\n", "\n", "    max_tokens=38192,\n", "    temperature=0.1,\n", "    top_p=0.5,\n", "    frequency_penalty=0,\n", "    presence_penalty=2,\n", ")\n", "\n", "# 只提取并打印文本内容\n", "print(chat_completion.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "18b0f6a2", "metadata": {}, "outputs": [], "source": ["python -m tools.api_server \\\n", "    --listen 0.0.0.0:7860 \\\n", "    --llama-checkpoint-path \"checkpoints/fish-speech-1.5\" \\\n", "    --decoder-checkpoint-path \"checkpoints/fish-speech-1.5/firefly-gan-vq-fsq-8x1024-21hz-generator.pth\" \\\n", "    --decoder-config-name firefly_gan_vq\\\n", "    --compile"]}, {"cell_type": "code", "execution_count": 5, "id": "f7733c4a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["指令解析结果：\n", "\n", "1. 前进 (F) 5 步\n", "2. 转向右 (R)\n", "3. 前进 (F) 3 步\n", "4. 如果 (IF) 看到障碍物 (OBSTACLE) 就停下 (S)根据您的指令，我会提取出以下操作指令序列：\n", "\n", "1. 向前走(F)\n", "2. 五步(5)\n", "3. 右转(R)\n", "4. 再前进(F)\n", "5. 三步(3)\n", "6. 如果看到障碍物(S)\n", "\n", "请注意，这只是一个可能的解析结果，具体的指令执行顺序可能会根据您的具体环境和行为进行调整。\n"]}], "source": ["# ... existing code ...\n", "\n", "# 修复后的代码 - 使用三引号来避免引号转义问题\n", "import openai\n", "openai.api_base = \"http://114.111.24.248:8901/v1\"\n", "openai.api_key = \"none\"\n", "\n", "for chunk in openai.ChatCompletion.create(\n", "    model=\"Qwen\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"\"\"你是一个高级指令解析系统。你需要从人类的自然语言描述中提取出具体的操作指令序列。\n", "\n", "任务定义：\n", "- 分析输入文本\n", "- 识别其中包含的动作指令\n", "- 按照文本中的逻辑顺序提取这些指令\n", "- 将复杂、隐含或抽象的表述转换为预定义的基本指令\n", "\n", "基本指令集：\n", "- 前进(F)\n", "- 后退(B)\n", "- 停止(S)\n", "- 转向左(L)\n", "- 转向右(R)\n", "- [其他必要的指令]\n", "\n", "输出格式：\n", "1. 提供一个按顺序编号的指令列表\n", "2. 每个指令都应该是基本指令集中的一个\n", "3. 可选：在括号中添加指令代码(F,B,S,L,R)\n", "\n", "解析规则：\n", "- 将间接表述映射到具体指令（例如\"向前移动一段距离\" → \"前进\"）\n", "- 识别条件语句并提取其中的指令（例如\"如果看到障碍物就停下\"包含\"停止\"指令）\n", "- 保持指令在原文中的相对顺序\n", "- 对于重复的动作，提取为多个相同的指令\"\"\"},\n", "        {\"role\": \"user\", \"content\": \"你好，请帮我解析这个指令：向前走五步，然后右转，再前进三步，如果看到障碍物就停下。\"}\n", "    ],\n", "    stream=True \n", "    # Specifying stop words in streaming output format is not yet supported and is under development.\n", "):\n", "    if hasattr(chunk.choices[0].delta, \"content\"):\n", "        print(chunk.choices[0].delta.content, end=\"\", flush=True)\n", "\n", "# create a request not activating streaming response\n", "response = openai.ChatCompletion.create(\n", "    model=\"Qwen\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"\"\"你是一个高级指令解析系统。你需要从人类的自然语言描述中提取出具体的操作指令序列。\n", "\n", "任务定义：\n", "- 分析输入文本\n", "- 识别其中包含的动作指令\n", "- 按照文本中的逻辑顺序提取这些指令\n", "- 将复杂、隐含或抽象的表述转换为预定义的基本指令\n", "\n", "基本指令集：\n", "- 前进(F)\n", "- 后退(B)\n", "- 停止(S)\n", "- 转向左(L)\n", "- 转向右(R)\n", "- [其他必要的指令]\n", "\n", "输出格式：\n", "1. 提供一个按顺序编号的指令列表\n", "2. 每个指令都应该是基本指令集中的一个\n", "3. 可选：在括号中添加指令代码(F,B,S,L,R)\n", "\n", "解析规则：\n", "- 将间接表述映射到具体指令（例如\"向前移动一段距离\" → \"前进\"）\n", "- 识别条件语句并提取其中的指令（例如\"如果看到障碍物就停下\"包含\"停止\"指令）\n", "- 保持指令在原文中的相对顺序\n", "- 对于重复的动作，提取为多个相同的指令\"\"\"},\n", "        {\"role\": \"user\", \"content\": \"你好，请帮我解析这个指令：向前走五步，然后右转，再前进三步，如果看到障碍物就停下。\"}\n", "    ],\n", "    stream=False,\n", "    stop=[] # You can add custom stop words here, e.g., stop=[\"Observation:\"] for ReAct prompting.\n", ")\n", "print(response.choices[0].message.content)\n"]}], "metadata": {"kernelspec": {"display_name": "oll<PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}