import openai

class QwenAPI:
    def __init__(self, api_base="http://114.111.24.248:8901/v1", api_key="none"):
        """
        初始化Qwen API客户端
        
        参数:
            api_base: API基础URL
            api_key: API密钥
        """
        openai.api_base = api_base
        openai.api_key = api_key
        
        self.default_system_prompt = """你是一个高级指令解析系统。你需要从人类的自然语言描述中提取出具体的操作指令序列。

任务定义：
- 分析输入文本
- 识别其中包含的动作指令
- 按照文本中的逻辑顺序提取这些指令
- 将复杂、隐含或抽象的表述转换为预定义的基本指令

基本指令集：
- 跟近点(N)
- 跟远点(F)
- 找东西(S)
- 停止(P)

输出格式：
1. 提供一个按顺序编号的指令列表
2. 每个指令都应该是基本指令集中的一个
3. 可选：在括号中添加指令代码(N,F,S,P,C)

解析规则：
- 将间接表述映射到具体指令（例如"离我近一点" → "跟近点"）
- 识别条件语句并提取其中的指令（例如"看到障碍物就停下"包含"停止"指令）
- 保持指令在原文中的相对顺序
- 对于重复的动作，提取为多个相同的指令


"""

    def chat(self, user_input, system_prompt=None, stop_words=None, temperature=0):
        """
        与Qwen模型进行对话
        
        参数:
            user_input: 用户输入的文本
            system_prompt: 系统提示词，如果为None则使用默认提示词
            stop_words: 停止词列表
            
        返回:
            模型的完整响应文本
        """
        system_prompt = system_prompt or self.default_system_prompt
        stop_words = stop_words or []
        
        response = openai.ChatCompletion.create(
            model="Qwen",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input}
            ],
            stream=False,
            stop=stop_words,
            temperature=temperature
        )
        return response.choices[0].message.content

# 使用示例
if __name__ == "__main__":
    # 创建API实例
    qwen = QwenAPI()
    
    # 测试用户输入
    test_input = "你好，请帮我解析这个指令：跟近一点停下后继续跟着我"
    
    # 测试非流式响应
    print("测试响应：")
    response = qwen.chat(test_input)
    print(response)