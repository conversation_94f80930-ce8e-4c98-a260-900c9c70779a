# 通用Web服务框架环境配置模板
# 复制此文件为 .env 并根据您的需求修改配置

# ==================== 基本配置 ====================

# 运行环境 (development, production, testing)
ENVIRONMENT=development

# 调试模式 (true, false)
DEBUG=true

# 应用名称
APP_NAME=通用Web服务框架

# 应用版本
APP_VERSION=1.0.0

# ==================== 服务器配置 ====================

# 服务器监听地址
SERVER_HOST=0.0.0.0

# 服务器监听端口
SERVER_PORT=5004

# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 最大文件上传大小 (字节)
MAX_FILE_SIZE=52428800

# ==================== SSL配置 ====================

# 是否启用SSL (true, false)
SSL_ENABLED=true

# SSL私钥文件路径
SSL_KEYFILE=ssl/key.pem

# SSL证书文件路径
SSL_CERTFILE=ssl/cert.pem

# ==================== 服务配置 ====================

# 服务类型 (example, funasr, text, image, custom)
SERVICE_TYPE=example

# FunASR模型配置
FUNASR_MODEL=iic/SenseVoiceSmall
FUNASR_DEVICE=cpu
FUNASR_LANGUAGE=auto

# 百度ASR配置
BAIDU_ASR_APPID=119647562
BAIDU_ASR_APPKEY=oJRBGday6ccLyjEmc01CUlyh
BAIDU_ASR_DEV_PID=15372
BAIDU_ASR_URI=ws://vop.baidu.com/realtime_asr

# ==================== 数据库配置 ====================

# 数据库连接URL (如果需要)
DATABASE_URL=sqlite:///./app.db

# Redis连接URL (如果需要)
REDIS_URL=redis://localhost:6379

# ==================== API配置 ====================

# API密钥 (如果需要)
API_KEY=your-api-key-here

# API速率限制 (请求/分钟)
API_RATE_LIMIT=100

# ==================== 存储配置 ====================

# 数据存储目录
DATA_DIR=data

# 模型文件目录
MODELS_DIR=models

# 日志文件目录
LOGS_DIR=logs

# 临时文件目录
TEMP_DIR=temp

# ==================== WebSocket配置 ====================

# 最大WebSocket连接数
MAX_WEBSOCKET_CONNECTIONS=100

# WebSocket心跳间隔 (秒)
WEBSOCKET_HEARTBEAT_INTERVAL=30

# ==================== 缓存配置 ====================

# 最大历史记录数
MAX_HISTORY_RECORDS=100

# 历史记录清理间隔 (秒)
HISTORY_CLEANUP_INTERVAL=3600

# 缓存过期时间 (秒)
CACHE_EXPIRE_TIME=1800

# ==================== 安全配置 ====================

# CORS允许的源 (逗号分隔)
CORS_ORIGINS=http://localhost:3000,https://localhost:3000

# 允许的文件类型 (逗号分隔)
ALLOWED_FILE_TYPES=.wav,.mp3,.txt,.jpg,.png,.pdf

# 最大请求大小 (字节)
MAX_REQUEST_SIZE=104857600

# ==================== 监控配置 ====================

# 是否启用监控 (true, false)
MONITORING_ENABLED=true

# 监控数据保留时间 (小时)
MONITORING_RETENTION_HOURS=24

# 性能指标收集间隔 (秒)
METRICS_COLLECTION_INTERVAL=60

# ==================== 第三方服务配置 ====================

# 邮件服务配置 (如果需要)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# 云存储配置 (如果需要)
CLOUD_STORAGE_BUCKET=your-bucket-name
CLOUD_STORAGE_REGION=us-east-1
CLOUD_STORAGE_ACCESS_KEY=your-access-key
CLOUD_STORAGE_SECRET_KEY=your-secret-key

# ==================== 开发配置 ====================

# 是否启用热重载 (true, false)
HOT_RELOAD=true

# 是否显示详细错误信息 (true, false)
SHOW_ERROR_DETAILS=true

# 是否启用API文档 (true, false)
ENABLE_API_DOCS=true

# API文档路径
API_DOCS_PATH=/docs

# ==================== 生产环境配置 ====================

# 工作进程数 (生产环境)
WORKERS=4

# 是否启用访问日志 (true, false)
ACCESS_LOG=true

# 访问日志文件路径
ACCESS_LOG_FILE=logs/access.log

# 错误日志文件路径
ERROR_LOG_FILE=logs/error.log

# ==================== 自定义配置 ====================

# 在这里添加您的自定义配置项
# CUSTOM_SETTING_1=value1
# CUSTOM_SETTING_2=value2
