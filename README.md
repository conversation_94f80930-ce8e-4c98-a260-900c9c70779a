# FunASR本地语音识别应用

## 🎯 项目概述

基于FunASR的本地语音识别服务，采用阿里达摩院开源的SenseVoice模型，支持中英文语音识别，无需网络依赖，保护隐私安全。

## ✨ 主要特性

- 🏠 **本地化处理**: 基于FunASR，无需联网，保护隐私
- 🎙️ **高质量识别**: 使用SenseVoice模型，识别准确率高
- ⚡ **实时处理**: WebSocket实时传输，低延迟响应
- 🌐 **跨平台支持**: 支持Windows、macOS、Linux
- 🔧 **简单易用**: 一键安装，开箱即用

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建conda环境
conda create -n asr-llm python=3.9
conda activate asr-llm

# 安装FunASR依赖
python setup_funasr.py
```

### 2. 启动服务

```bash
conda activate asr-llm
python main.py
```

### 3. 访问应用

- **桌面端**: `https://localhost:5004`
- **移动端**: `https://your-ip:5004`

## 📋 详细安装步骤

### 第一步：创建Conda环境

```bash
# 创建专用环境
conda create -n asr-llm python=3.9 -y
conda activate asr-llm
```

### 第二步：自动安装FunASR

```bash
# 运行自动安装脚本
python setup_funasr.py
```

该脚本将自动：
- 安装FunASR及相关依赖
- 配置PyTorch环境
- 验证安装完整性
- 准备模型下载（首次使用时自动下载）

### 第三步：手动安装（可选）

如果自动安装失败，可以手动安装：

```bash
# 安装核心依赖
pip install funasr==1.0.23
pip install torch>=1.13.0 torchaudio>=0.13.0
pip install numpy soundfile librosa
pip install fastapi uvicorn websockets python-multipart requests

# 验证安装
python -c "from funasr import AutoModel; print('FunASR安装成功')"
```

## 🎙️ 使用指南

### Android设备使用流程

1. **访问应用**: 在Chrome浏览器中访问 `https://your-ip:5004`
2. **证书信任**: 点击"高级" → "继续访问"（自签名证书）
3. **权限授予**: 允许麦克风访问权限
4. **开始录音**: 点击麦克风按钮开始录音
5. **停止录音**: 再次点击停止录音，等待识别结果

### 自动服务切换

- **优先使用**: Web Speech API（如果可用）
- **自动降级**: 检测到Google服务受限时自动切换到FunASR
- **无缝体验**: 用户无需手动配置，系统自动选择最佳方案

## 🔧 技术架构

### 后端服务
```
FastAPI + FunASR
├── main.py                 # 主服务文件
├── FunASRService          # FunASR服务封装
├── WebSocket管理          # 实时通信
└── API端点               # RESTful接口
```

### 前端界面
```
HTML5 + JavaScript
├── Web Speech API        # 浏览器原生语音识别
├── Web Audio API         # 音频录制处理
├── WebSocket客户端       # 实时通信
└── FunASR集成           # 本地识别服务
```

### 识别流程
```
用户录音 → Web Audio API → 音频数据 → 
FunASR模型 → 文本结果 → WebSocket → 前端显示
```

## 🛠️ 配置说明

### 服务器配置

```python
# main.py 中的关键配置
uvicorn.run(
    app, 
    host="0.0.0.0", 
    port=5004,
    ssl_keyfile="ssl/server.key",
    ssl_certfile="ssl/server.crt"
)
```

### FunASR模型配置

```python
# 使用SenseVoice小模型
model = AutoModel(
    model="iic/SenseVoiceSmall",
    vad_model="fsmn-vad", 
    vad_kwargs={"max_single_segment_time": 30000},
    device="cpu"  # 或 "cuda" 如果有GPU
)
```

## 📱 移动端优化

### Android兼容性
- ✅ Chrome浏览器完美支持
- ✅ 自动检测Google服务可用性
- ✅ 本地FunASR无网络依赖
- ✅ 自签名证书处理

### iOS兼容性
- ✅ Safari浏览器原生支持
- ✅ Web Speech API优先
- ✅ FunASR作为备选方案

## 🔍 故障排除

### 常见问题

#### 1. 模型下载失败
```bash
# 手动下载模型
python -c "
from funasr import AutoModel
model = AutoModel(model='iic/SenseVoiceSmall')
print('模型下载完成')
"
```

#### 2. Android录音无响应
- 检查麦克风权限
- 确认HTTPS访问
- 查看浏览器控制台错误

#### 3. 识别结果为空
- 确认音频质量
- 检查网络连接
- 查看服务端日志

### 日志调试

```bash
# 启动服务时查看详细日志
python main.py

# 查看FunASR初始化过程
# 日志会显示模型加载进度
```

## 📊 性能优化

### 硬件建议
- **CPU**: 4核以上推荐
- **内存**: 8GB以上推荐
- **存储**: 预留2GB用于模型文件
- **GPU**: 可选，使用CUDA加速

### 模型选择
- **SenseVoiceSmall**: 平衡性能和准确率（默认）
- **SenseVoiceLarge**: 更高准确率，需要更多资源

## 🔒 安全考虑

### 隐私保护
- ✅ 本地处理，音频数据不上传云端
- ✅ 临时文件自动清理
- ✅ 无第三方数据依赖

### 网络安全
- ✅ HTTPS加密传输
- ✅ WebSocket安全连接
- ✅ 自签名证书支持

## 📈 扩展功能

### 支持的功能
- [x] 中文语音识别
- [x] 英文语音识别
- [x] 混合语言识别
- [x] 实时语音转文字
- [x] 智能标点符号
- [x] 移动端适配

### 计划功能
- [ ] 多语言支持扩展
- [ ] 语音情感识别
- [ ] 说话人识别
- [ ] 音频降噪优化

## 🤝 技术支持

### 获取帮助
1. 查看控制台日志获取详细错误信息
2. 检查conda环境是否正确激活
3. 确认FunASR依赖是否完整安装
4. 验证模型文件是否正确下载

### 常用命令
```bash
# 检查环境
conda info --envs

# 重新安装依赖
pip install -r requirements.txt

# 测试FunASR
python -c "from funasr import AutoModel; print('OK')"
```

---

## 📄 许可证

本项目基于MIT许可证开源，详见LICENSE文件。

## 🙏 致谢

- 阿里达摩院FunASR项目
- SenseVoice语音识别模型
- 开源社区的支持与贡献 