# 通用Web服务框架

## 🎯 项目概述

这是一个基于FastAPI的通用Web服务框架模板，提供了完整的Web服务基础架构，包括WebSocket支持、文件上传、监控功能等。您可以基于此框架快速构建各种Web服务应用。

## ✨ 主要特性

- 🚀 **现代化架构**: 基于FastAPI，支持异步处理和自动API文档
- 🔌 **WebSocket支持**: 内置WebSocket连接管理，支持实时通信
- 📁 **文件处理**: 支持文件上传和处理，可扩展多种文件类型
- 🔧 **可配置**: 灵活的配置系统，支持环境变量和配置文件
- 🛡️ **安全性**: 支持HTTPS、CORS、文件类型验证等安全特性
- 📊 **监控功能**: 内置监控页面和数据记录功能
- 🎨 **模块化设计**: 清晰的代码结构，易于扩展和维护
- 🌐 **跨平台**: 支持Windows、macOS、Linux

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建Python虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

```bash
# 复制环境配置模板
cp .env.template .env

# 编辑配置文件（根据需要修改）
# 编辑 .env 文件设置您的配置
```

### 3. 启动服务

```bash
# 启动服务器
python main.py
```

### 4. 访问应用

- **主页面**: `http://localhost:5004` 或 `https://localhost:5004`（如果启用SSL）
- **监控页面**: `http://localhost:5004/monitor`
- **API文档**: `http://localhost:5004/docs`

## 📋 详细配置指南

### 第一步：基础配置

1. **复制配置模板**：
```bash
cp .env.template .env
```

2. **编辑配置文件**：
根据您的需求修改 `.env` 文件中的配置项：

```bash
# 基本服务配置
SERVER_HOST=0.0.0.0
SERVER_PORT=5004
SERVICE_TYPE=example  # 可选: example, funasr, text, image, custom

# SSL配置（可选）
SSL_ENABLED=true
SSL_KEYFILE=ssl/key.pem
SSL_CERTFILE=ssl/cert.pem
```

### 第二步：选择服务类型

框架支持多种服务类型，在 `config.py` 中配置：

1. **示例服务** (`example`): 基础模板服务
2. **语音识别服务** (`funasr`): 需要安装FunASR相关依赖
3. **文本处理服务** (`text`): 文本分析和处理
4. **图像处理服务** (`image`): 图像识别和处理
5. **自定义服务** (`custom`): 您自己实现的服务

### 第三步：安装特定依赖（可选）

根据选择的服务类型安装相应依赖：

```bash
# 语音识别服务依赖
pip install funasr torch torchaudio soundfile librosa modelscope

# 图像处理服务依赖
pip install Pillow opencv-python

# 文本处理服务依赖
pip install transformers jieba

# 数据库支持（可选）
pip install sqlalchemy alembic
```

## 🎯 使用指南

### 基础使用

1. **启动服务**：
```bash
python main.py
```

2. **访问Web界面**：
   - 主页面: `http://localhost:5004`
   - 监控页面: `http://localhost:5004/monitor`
   - API文档: `http://localhost:5004/docs`

3. **文件上传处理**：
   - 通过Web界面上传文件
   - 或使用API端点 `/api/service/process`

### API使用示例

```python
import requests

# 上传文件进行处理
with open('test_file.txt', 'rb') as f:
    response = requests.post(
        'http://localhost:5004/api/service/process',
        files={'data_file': f}
    )
    result = response.json()
    print(result)

# 获取服务状态
response = requests.get('http://localhost:5004/api/service/status')
status = response.json()
print(status)
```

### WebSocket使用

```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:5004/ws');

// 发送数据
ws.send(JSON.stringify({
    type: 'text',
    content: 'Hello, World!'
}));

// 接收响应
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到响应:', data);
};
```

## 🏗️ 技术架构

### 核心架构
```
通用Web服务框架
├── main.py                 # 主服务文件
├── config.py              # 配置管理
├── service_template.py    # 服务模板
├── BaseService           # 抽象服务接口
├── ConnectionManager     # WebSocket连接管理
└── API路由              # RESTful接口
```

### 服务层架构
```
BaseService (抽象基类)
├── ExampleService        # 示例服务实现
├── FunASRService        # 语音识别服务
├── TextProcessingService # 文本处理服务
├── ImageProcessingService # 图像处理服务
└── CustomService        # 自定义服务
```

### 数据流程
```
客户端请求 → FastAPI路由 → 服务处理 →
结果返回 → WebSocket广播 → 监控更新
```

### 目录结构
```
project/
├── main.py              # 主服务文件
├── config.py            # 配置文件
├── service_template.py  # 服务模板
├── requirements.txt     # 依赖列表
├── .env.template       # 环境配置模板
├── static/             # 静态文件
│   ├── index.html      # 主页面
│   └── monitor.html    # 监控页面
├── ssl/                # SSL证书目录
├── data/               # 数据存储目录
├── models/             # 模型文件目录
└── logs/               # 日志文件目录
```

## 🛠️ 开发指南

### 创建自定义服务

1. **继承BaseService类**：
```python
from service_template import BaseService

class MyCustomService(BaseService):
    def __init__(self):
        super().__init__("我的自定义服务")

    async def initialize(self) -> bool:
        # 初始化逻辑
        self.is_initialized = True
        return True

    async def process_data(self, data: bytes) -> dict:
        # 数据处理逻辑
        return {"success": True, "result": "处理完成"}

    def get_status(self) -> dict:
        # 状态信息
        return {"service": self.service_name, "initialized": self.is_initialized}
```

2. **在main.py中使用**：
```python
# 替换默认服务
current_service = MyCustomService()
```

### 配置管理

框架支持多种配置方式：

1. **环境变量** (`.env`文件)
2. **配置类** (`config.py`)
3. **运行时配置**

```python
from config import get_config

config = get_config()
server_config = config['server']
```

## 🔌 扩展功能

### WebSocket集成

框架内置WebSocket支持，可用于：
- 实时数据传输
- 状态监控
- 进度更新
- 双向通信

### 文件处理

支持多种文件类型处理：
- 文本文件 (`.txt`, `.json`, `.csv`)
- 图像文件 (`.jpg`, `.png`, `.gif`)
- 音频文件 (`.wav`, `.mp3`, `.m4a`)
- 自定义文件类型

### 监控功能

内置监控系统：
- 实时状态监控
- 历史记录查看
- 性能指标统计
- 错误日志记录

## 🔍 故障排除

### 常见问题

#### 1. 服务启动失败
```bash
# 检查端口是否被占用
netstat -an | grep 5004

# 检查依赖是否安装完整
pip install -r requirements.txt

# 查看详细错误信息
python main.py
```

#### 2. SSL证书问题
```bash
# 生成自签名证书（开发环境）
mkdir ssl
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes

# 或禁用SSL
# 在.env文件中设置: SSL_ENABLED=false
```

#### 3. 文件上传失败
- 检查文件大小限制 (默认50MB)
- 确认文件类型是否支持
- 查看服务器日志获取详细错误

#### 4. WebSocket连接失败
- 确认防火墙设置
- 检查代理配置
- 验证WebSocket URL格式

### 日志调试

```bash
# 启动服务并查看日志
python main.py

# 设置详细日志级别
# 在.env文件中设置: LOG_LEVEL=DEBUG
```

## 📊 性能优化

### 硬件建议
- **CPU**: 2核以上推荐
- **内存**: 4GB以上推荐
- **存储**: 预留1GB用于数据和日志
- **网络**: 稳定的网络连接（如需外部API）

### 生产环境配置
```bash
# 使用多进程部署
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker

# 或使用Docker部署
docker build -t web-service-framework .
docker run -p 5004:5004 web-service-framework
```

## 🔒 安全考虑

### 数据安全
- ✅ 本地数据处理，保护隐私
- ✅ 临时文件自动清理
- ✅ 可配置的文件类型限制
- ✅ 文件大小限制保护

### 网络安全
- ✅ HTTPS加密传输支持
- ✅ CORS跨域保护
- ✅ WebSocket安全连接
- ✅ API访问控制

## 📈 扩展示例

### 已实现的服务类型
- [x] 示例服务 (基础模板)
- [x] 语音识别服务 (FunASR)
- [x] 文本处理服务
- [x] 图像处理服务
- [x] WebSocket实时通信
- [x] 文件上传处理

### 可扩展的功能
- [ ] 数据库集成
- [ ] 缓存系统 (Redis)
- [ ] 消息队列
- [ ] 微服务架构
- [ ] 容器化部署
- [ ] 监控和告警

## 🤝 贡献指南

### 如何贡献
1. Fork 本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发环境设置
```bash
# 克隆项目
git clone <your-repo-url>
cd web-service-framework

# 安装开发依赖
pip install -r requirements.txt
pip install pytest pytest-asyncio httpx  # 测试依赖

# 运行测试
pytest
```

---

## 📄 许可证

本项目基于MIT许可证开源，详见LICENSE文件。

## 🙏 致谢

- FastAPI框架
- Uvicorn ASGI服务器
- 开源社区的支持与贡献

---

**🚀 开始构建您的Web服务应用吧！**