<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度实时语音识别</title>
    <style>
        :root {
            --primary: #3a86ff;
            --secondary: #00f5d4;
            --accent: #8338ec;
            --success: #06d6a0;
            --warning: #ffd60a;
            --error: #ef476f;
            --dark: #0f0e17;
            --text: #fffffe;
            --panel: rgba(15, 23, 42, 0.8);
            --border: rgba(255, 255, 255, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--dark);
            color: var(--text);
            min-height: 100vh;
            background-image: 
                radial-gradient(circle at 20% 35%, rgba(58, 134, 255, 0.15) 0%, transparent 29%),
                radial-gradient(circle at 70% 65%, rgba(131, 56, 236, 0.1) 0%, transparent 20%);
        }

        .container {
            max-width: 1000px;
            padding: 20px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .subtitle {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 20px;
        }

        .status-bar {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .status-item {
            background: var(--panel);
            padding: 10px 20px;
            border-radius: 25px;
            border: 1px solid var(--border);
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--error);
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: var(--success);
        }

        .status-dot.recording {
            background: var(--warning);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .control-panel {
            background: var(--panel);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid var(--border);
            text-align: center;
        }

        .record-button {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
            overflow: hidden;
        }

        .record-button:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 30px rgba(58, 134, 255, 0.3);
        }

        .record-button.recording {
            background: linear-gradient(135deg, var(--error), var(--warning));
            animation: recording-pulse 1.5s infinite;
        }

        .record-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        @keyframes recording-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--panel);
            color: var(--text);
            border: 1px solid var(--border);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn.primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border: none;
        }

        .btn.danger {
            background: linear-gradient(135deg, var(--error), #ff6b9d);
            border: none;
        }

        .results-panel {
            background: var(--panel);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid var(--border);
            min-height: 300px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border);
        }

        .results-title {
            font-size: 1.3rem;
            font-weight: 600;
        }

        .clear-btn {
            padding: 8px 16px;
            font-size: 0.8rem;
            background: transparent;
            color: var(--error);
            border: 1px solid var(--error);
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .clear-btn:hover {
            background: var(--error);
            color: white;
        }

        .result-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid var(--primary);
            transition: all 0.3s ease;
        }

        .result-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateX(5px);
        }

        .result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .result-text {
            font-size: 1.1rem;
            line-height: 1.6;
            word-break: break-all;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            opacity: 0.6;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .log-panel {
            background: var(--panel);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid var(--border);
            max-height: 200px;
            overflow-y: auto;
        }

        .log-item {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.8rem;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0.8;
        }

        .log-item:last-child {
            border-bottom: none;
        }

        .log-time {
            color: var(--secondary);
            margin-right: 10px;
        }

        .log-level {
            margin-right: 10px;
            font-weight: 600;
        }

        .log-level.info { color: var(--primary); }
        .log-level.error { color: var(--error); }
        .log-level.success { color: var(--success); }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .status-bar {
                gap: 10px;
            }
            
            .status-item {
                padding: 8px 15px;
                font-size: 0.8rem;
            }
            
            .record-button {
                width: 100px;
                height: 100px;
                font-size: 1rem;
            }
            
            .control-panel, .results-panel {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🎙️ 百度实时语音识别</h1>
            <p class="subtitle">基于百度WebSocket API的实时语音转文字服务</p>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-dot" id="connectionStatus"></div>
                <span id="connectionText">未连接</span>
            </div>
            <div class="status-item">
                <div class="status-dot" id="recordingStatus"></div>
                <span id="recordingText">未录音</span>
            </div>
            <div class="status-item">
                <span id="sessionInfo">会话: 未开始</span>
            </div>
        </div>

        <div class="control-panel">
            <button class="record-button" id="recordButton" disabled>
                <span id="recordButtonText">连接中...</span>
            </button>
            
            <div class="button-group">
                <button class="btn primary" id="connectButton">连接服务</button>
                <button class="btn" id="finishButton" disabled>结束识别</button>
                <button class="btn danger" id="disconnectButton" disabled>断开连接</button>
            </div>
        </div>

        <div class="results-panel">
            <div class="results-header">
                <h3 class="results-title">识别结果</h3>
                <button class="clear-btn" id="clearResults">清空结果</button>
            </div>
            
            <div id="resultsContainer">
                <div class="empty-state">
                    <div class="empty-icon">🎯</div>
                    <p>点击录音按钮开始语音识别</p>
                </div>
            </div>
        </div>

        <div class="log-panel">
            <div id="logContainer"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let asrWebSocket = null;
        let mediaRecorder = null;
        let audioStream = null;
        let isRecording = false;
        let isConnected = false;
        let sessionId = null;
        let resultCount = 0;

        // DOM元素
        const elements = {
            connectionStatus: document.getElementById('connectionStatus'),
            connectionText: document.getElementById('connectionText'),
            recordingStatus: document.getElementById('recordingStatus'),
            recordingText: document.getElementById('recordingText'),
            sessionInfo: document.getElementById('sessionInfo'),
            recordButton: document.getElementById('recordButton'),
            recordButtonText: document.getElementById('recordButtonText'),
            connectButton: document.getElementById('connectButton'),
            finishButton: document.getElementById('finishButton'),
            disconnectButton: document.getElementById('disconnectButton'),
            clearResults: document.getElementById('clearResults'),
            resultsContainer: document.getElementById('resultsContainer'),
            logContainer: document.getElementById('logContainer')
        };

        // 日志函数
        function addLog(level, message) {
            const time = new Date().toLocaleTimeString();
            const logItem = document.createElement('div');
            logItem.className = 'log-item';
            logItem.innerHTML = `
                <span class="log-time">${time}</span>
                <span class="log-level ${level}">[${level.toUpperCase()}]</span>
                <span>${message}</span>
            `;
            elements.logContainer.appendChild(logItem);
            elements.logContainer.scrollTop = elements.logContainer.scrollHeight;
        }

        // 更新状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            elements.connectionStatus.className = `status-dot ${connected ? 'connected' : ''}`;
            elements.connectionText.textContent = connected ? '已连接' : '未连接';
            
            elements.connectButton.disabled = connected;
            elements.disconnectButton.disabled = !connected;
            elements.recordButton.disabled = !connected;
            
            if (connected) {
                elements.recordButtonText.textContent = '开始录音';
            } else {
                elements.recordButtonText.textContent = '未连接';
                updateRecordingStatus(false);
            }
        }

        function updateRecordingStatus(recording) {
            isRecording = recording;
            elements.recordingStatus.className = `status-dot ${recording ? 'recording' : ''}`;
            elements.recordingText.textContent = recording ? '录音中' : '未录音';
            elements.recordButton.className = `record-button ${recording ? 'recording' : ''}`;
            elements.recordButtonText.textContent = recording ? '停止录音' : '开始录音';
            elements.finishButton.disabled = !recording;
        }

        function updateSessionInfo(info) {
            if (info) {
                sessionId = info;
                elements.sessionInfo.textContent = `会话: ${info.substring(0, 8)}...`;
            } else {
                sessionId = null;
                elements.sessionInfo.textContent = '会话: 未开始';
            }
        }

        // WebSocket连接
        function connectASR() {
            if (asrWebSocket) {
                asrWebSocket.close();
            }

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/asr_ws`;
            
            addLog('info', `正在连接到 ${wsUrl}`);
            
            asrWebSocket = new WebSocket(wsUrl);
            
            asrWebSocket.onopen = function() {
                addLog('success', 'WebSocket连接已建立');
                updateConnectionStatus(true);
            };
            
            asrWebSocket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    handleASRMessage(data);
                } catch (e) {
                    addLog('error', `解析消息失败: ${e.message}`);
                }
            };
            
            asrWebSocket.onerror = function(error) {
                addLog('error', `WebSocket错误: ${error}`);
                updateConnectionStatus(false);
            };
            
            asrWebSocket.onclose = function() {
                addLog('info', 'WebSocket连接已关闭');
                updateConnectionStatus(false);
                updateSessionInfo(null);
            };
        }

        // 处理ASR消息
        function handleASRMessage(data) {
            switch (data.type) {
                case 'connected':
                    addLog('success', `ASR会话已建立: ${data.session_id}`);
                    updateSessionInfo(data.session_id);
                    break;
                    
                case 'recognition_result':
                    handleRecognitionResult(data.result);
                    break;
                    
                case 'websocket_error':
                    addLog('error', `ASR错误: ${data.error}`);
                    break;
                    
                case 'disconnected':
                    addLog('info', 'ASR会话已断开');
                    updateSessionInfo(null);
                    break;
                    
                default:
                    addLog('info', `收到消息: ${JSON.stringify(data)}`);
            }
        }

        // 处理识别结果
        function handleRecognitionResult(result) {
            addLog('success', `识别结果: ${JSON.stringify(result)}`);
            
            // 显示结果
            if (result && (result.partial_result || result.final_result)) {
                const text = result.partial_result || result.final_result;
                if (text && text.trim()) {
                    addRecognitionResult(text, result.type || 'partial');
                }
            }
        }

        // 添加识别结果到界面
        function addRecognitionResult(text, type) {
            // 清空空状态
            if (elements.resultsContainer.querySelector('.empty-state')) {
                elements.resultsContainer.innerHTML = '';
            }
            
            resultCount++;
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            resultItem.innerHTML = `
                <div class="result-meta">
                    <span>#${resultCount} - ${type === 'final' ? '最终结果' : '临时结果'}</span>
                    <span>${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="result-text">${text}</div>
            `;
            
            elements.resultsContainer.appendChild(resultItem);
            elements.resultsContainer.scrollTop = elements.resultsContainer.scrollHeight;
        }

        // 开始录音
        async function startRecording() {
            try {
                audioStream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });
                
                mediaRecorder = new MediaRecorder(audioStream, {
                    mimeType: 'audio/webm;codecs=opus'
                });
                
                mediaRecorder.ondataavailable = function(event) {
                    if (event.data.size > 0 && asrWebSocket && asrWebSocket.readyState === WebSocket.OPEN) {
                        // 将音频数据发送到服务器
                        asrWebSocket.send(event.data);
                    }
                };
                
                mediaRecorder.start(100); // 每100ms发送一次数据
                updateRecordingStatus(true);
                addLog('success', '开始录音');
                
            } catch (error) {
                addLog('error', `录音失败: ${error.message}`);
            }
        }

        // 停止录音
        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
                updateRecordingStatus(false);
                addLog('info', '停止录音');
            }
            
            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
                audioStream = null;
            }
        }

        // 结束识别
        function finishRecognition() {
            if (asrWebSocket && asrWebSocket.readyState === WebSocket.OPEN) {
                asrWebSocket.send(JSON.stringify({ type: 'finish' }));
                addLog('info', '发送结束识别命令');
            }
            stopRecording();
        }

        // 断开连接
        function disconnect() {
            stopRecording();
            if (asrWebSocket) {
                asrWebSocket.close();
            }
            updateConnectionStatus(false);
            updateSessionInfo(null);
        }

        // 清空结果
        function clearResults() {
            elements.resultsContainer.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">🎯</div>
                    <p>点击录音按钮开始语音识别</p>
                </div>
            `;
            resultCount = 0;
            addLog('info', '已清空识别结果');
        }

        // 事件监听器
        elements.connectButton.addEventListener('click', connectASR);
        elements.disconnectButton.addEventListener('click', disconnect);
        elements.finishButton.addEventListener('click', finishRecognition);
        elements.clearResults.addEventListener('click', clearResults);

        elements.recordButton.addEventListener('click', function() {
            if (!isConnected) return;
            
            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        });

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('info', '页面加载完成');
            updateConnectionStatus(false);
            updateRecordingStatus(false);
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            disconnect();
        });
    </script>
</body>
</html>
