#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用Web服务框架配置文件

此文件包含服务器和应用程序的配置选项。
根据您的具体需求修改这些配置。
"""

import os
from typing import Dict, Any

# ==================== 服务器配置 ====================

class ServerConfig:
    """服务器配置类"""
    
    # 服务器基本配置
    HOST = "0.0.0.0"  # 监听地址，0.0.0.0表示监听所有网络接口
    PORT = 5004       # 监听端口
    
    # SSL配置
    SSL_ENABLED = True  # 是否启用SSL
    SSL_KEYFILE = "ssl/key.pem"    # SSL私钥文件路径
    SSL_CERTFILE = "ssl/cert.pem"  # SSL证书文件路径
    
    # 日志配置
    LOG_LEVEL = "INFO"  # 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
    
    # 文件上传配置
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 最大文件大小 (50MB)
    
    # WebSocket配置
    MAX_CONNECTIONS = 100  # 最大WebSocket连接数
    HEARTBEAT_INTERVAL = 30  # 心跳间隔（秒）

# ==================== 应用配置 ====================

class AppConfig:
    """应用程序配置类"""
    
    # 应用基本信息
    APP_NAME = "通用Web服务框架"
    APP_VERSION = "1.0.0"
    APP_DESCRIPTION = "基于FastAPI的通用Web服务框架模板"
    
    # 静态文件配置
    STATIC_DIR = "static"  # 静态文件目录
    TEMPLATES_DIR = "templates"  # 模板文件目录（如果使用）
    
    # 数据存储配置
    DATA_DIR = "data"  # 数据存储目录
    MODELS_DIR = "models"  # 模型文件目录
    LOGS_DIR = "logs"  # 日志文件目录
    
    # 历史记录配置
    MAX_HISTORY_RECORDS = 100  # 最大历史记录数
    HISTORY_CLEANUP_INTERVAL = 3600  # 历史记录清理间隔（秒）

# ==================== 服务配置 ====================

class ServiceConfig:
    """服务配置类"""
    
    # 服务类型选择
    # 可选值: "example", "funasr", "custom"
    SERVICE_TYPE = "example"
    
    # 示例服务配置
    EXAMPLE_SERVICE = {
        "name": "示例服务",
        "description": "这是一个示例服务实现",
        "enabled": True
    }
    
    # FunASR服务配置
    FUNASR_SERVICE = {
        "name": "FunASR语音识别服务",
        "model": "iic/SenseVoiceSmall",  # 模型名称
        "device": "cpu",  # 计算设备: cpu, cuda
        "language": "auto",  # 语言设置: auto, zh, en
        "enabled": False
    }
    
    # 自定义服务配置
    CUSTOM_SERVICE = {
        "name": "自定义服务",
        "description": "请在此处配置您的自定义服务",
        "enabled": False
    }

# ==================== 环境配置 ====================

class EnvironmentConfig:
    """环境配置类"""
    
    # 运行环境
    ENVIRONMENT = os.getenv("ENVIRONMENT", "development")  # development, production, testing
    
    # 调试模式
    DEBUG = os.getenv("DEBUG", "True").lower() == "true"
    
    # 数据库配置（如果需要）
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./app.db")
    
    # Redis配置（如果需要）
    REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    # API密钥（如果需要）
    API_KEY = os.getenv("API_KEY", "your-api-key-here")

# ==================== 配置工厂 ====================

def get_config() -> Dict[str, Any]:
    """获取完整配置字典"""
    return {
        "server": {
            "host": ServerConfig.HOST,
            "port": ServerConfig.PORT,
            "ssl_enabled": ServerConfig.SSL_ENABLED,
            "ssl_keyfile": ServerConfig.SSL_KEYFILE,
            "ssl_certfile": ServerConfig.SSL_CERTFILE,
            "log_level": ServerConfig.LOG_LEVEL,
            "max_file_size": ServerConfig.MAX_FILE_SIZE,
            "max_connections": ServerConfig.MAX_CONNECTIONS,
            "heartbeat_interval": ServerConfig.HEARTBEAT_INTERVAL
        },
        "app": {
            "name": AppConfig.APP_NAME,
            "version": AppConfig.APP_VERSION,
            "description": AppConfig.APP_DESCRIPTION,
            "static_dir": AppConfig.STATIC_DIR,
            "templates_dir": AppConfig.TEMPLATES_DIR,
            "data_dir": AppConfig.DATA_DIR,
            "models_dir": AppConfig.MODELS_DIR,
            "logs_dir": AppConfig.LOGS_DIR,
            "max_history_records": AppConfig.MAX_HISTORY_RECORDS,
            "history_cleanup_interval": AppConfig.HISTORY_CLEANUP_INTERVAL
        },
        "service": {
            "type": ServiceConfig.SERVICE_TYPE,
            "example": ServiceConfig.EXAMPLE_SERVICE,
            "funasr": ServiceConfig.FUNASR_SERVICE,
            "custom": ServiceConfig.CUSTOM_SERVICE
        },
        "environment": {
            "env": EnvironmentConfig.ENVIRONMENT,
            "debug": EnvironmentConfig.DEBUG,
            "database_url": EnvironmentConfig.DATABASE_URL,
            "redis_url": EnvironmentConfig.REDIS_URL,
            "api_key": EnvironmentConfig.API_KEY
        }
    }

def create_directories():
    """创建必要的目录"""
    directories = [
        AppConfig.DATA_DIR,
        AppConfig.MODELS_DIR,
        AppConfig.LOGS_DIR,
        "ssl"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")

if __name__ == "__main__":
    # 创建必要的目录
    create_directories()
    
    # 打印配置信息
    config = get_config()
    print("当前配置:")
    for section, settings in config.items():
        print(f"\n[{section.upper()}]")
        for key, value in settings.items():
            print(f"  {key}: {value}")
