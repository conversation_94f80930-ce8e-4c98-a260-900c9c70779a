import{l as z,m as X,p as Y,q as Z,i as p,v as ee,w as te,r as y,a as se,x as q,d as oe,y as ne,c as G,o as ae,s as g,b as w,e as k,z as v,h as B,f as t,n as F,u as R,t as h,g as H,A as J,F as le,j as re,k as b,_ as ce}from"./index-bac5b045.js";import{a as ie,A as ue}from"./api-448a60d8.js";let $;const de={title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1,destroyOnClose:!1};let me=z({},de);function ve(){({instance:$}=Y({setup(){const{state:C,toggle:s}=Z();return()=>p(te,ee(C,{"onUpdate:show":s}),null)}}))}function pe(r){return X?new Promise((C,s)=>{$||ve(),$.open(z({},me,r,{callback:a=>{(a==="confirm"?C:s)(a)}}))}):Promise.resolve(void 0)}const j=r=>pe(z({showCancelButton:!0},r));var L=(r=>(r[r.CONNECTING=0]="CONNECTING",r[r.OPEN=1]="OPEN",r[r.CLOSING=2]="CLOSING",r[r.CLOSED=3]="CLOSED",r))(L||{});function _e(r,C={}){const s={url:r,reconnectInterval:3e3,maxReconnectAttempts:5,heartbeatInterval:3e4,...C},a=y(null),m=y(!1),d=y(!1),i=y(0),S=y(null);let N=null,_=null;const n={},D=e=>ie(e),x=()=>{var e;if(!(((e=a.value)==null?void 0:e.readyState)===L.OPEN||d.value))try{d.value=!0;const o=D(s.url);a.value=new WebSocket(o,s.protocols),a.value.onopen=c=>{var u;console.log(`WebSocket 连接已建立: ${o}`),m.value=!0,d.value=!1,i.value=0,S.value=null,A(),(u=n.onOpen)==null||u.call(n,c)},a.value.onmessage=c=>{var u,P;try{(u=n.onMessage)==null||u.call(n,c)}catch(U){console.warn("WebSocket 消息解析失败:",U),(P=n.onMessage)==null||P.call(n,c)}},a.value.onerror=c=>{var u;console.error("WebSocket 错误:",c),S.value=c,d.value=!1,(u=n.onError)==null||u.call(n,c)},a.value.onclose=c=>{var u;console.log("WebSocket 连接已关闭:",c.code,c.reason),m.value=!1,d.value=!1,I(),(u=n.onClose)==null||u.call(n,c),i.value<s.maxReconnectAttempts&&M()}}catch(o){console.error("WebSocket 连接失败:",o),d.value=!1,S.value=o}},O=()=>{N&&(clearTimeout(N),N=null),I(),a.value&&(a.value.close(1e3,"Manual disconnect"),a.value=null),m.value=!1,d.value=!1},T=e=>{if(!a.value||a.value.readyState!==L.OPEN)return console.warn("WebSocket 未连接，无法发送消息"),!1;try{const o={type:"message",data:e,timestamp:new Date().toISOString(),id:Date.now().toString()};return a.value.send(JSON.stringify(o)),!0}catch(o){return console.error("WebSocket 发送消息失败:",o),!1}},M=()=>{if(i.value>=s.maxReconnectAttempts){console.error("WebSocket 重连次数已达上限");return}i.value++,console.log(`WebSocket 将在 ${s.reconnectInterval}ms 后进行第 ${i.value} 次重连`),N=setTimeout(()=>{var e;(e=n.onReconnect)==null||e.call(n,i.value),x()},s.reconnectInterval)},A=()=>{s.heartbeatInterval&&(_=setInterval(()=>{m.value&&T({type:"heartbeat",timestamp:Date.now()})},s.heartbeatInterval))},I=()=>{_&&(clearInterval(_),_=null)},E=()=>{var e;return((e=a.value)==null?void 0:e.readyState)??L.CLOSED},W=(e,o)=>{n[e]=o},l={connect:x,disconnect:O,send:T,getState:E,isConnected:()=>m.value,reconnect:()=>{O(),q(()=>x())}};return se(()=>{O()}),{isConnected:m,isConnecting:d,reconnectAttempts:i,lastError:S,connect:x,disconnect:O,send:T,on:W,manager:l,getState:E,getWSUrl:D}}const fe={class:"monitor-panel-page"},ge={class:"page-content"},ye={class:"status-bar"},he={class:"status-info"},Ce={class:"status-indicator"},we={class:"status-text"},be={class:"connection-stats"},Se={class:"stat-item"},Ne={key:0,class:"stat-item"},xe={class:"monitor-content"},Oe={key:1,class:"records-list"},Te={class:"record-header"},ke={class:"record-time"},De={class:"record-text"},Ie={class:"record-actions"},Ee={class:"bottom-stats"},Pe={class:"stat-card"},Be={class:"stat-value"},Re={class:"stat-card"},Le={class:"stat-value"},Me={class:"stat-card"},Ae={class:"stat-value"},We=oe({__name:"MonitorPanel",setup(r){const C=ne(),s=y([]),a=y(!1),m=y(""),d=y(),{isConnected:i,isConnecting:S,connect:N,on:_}=_e(ue.ENDPOINTS.WS_VOICE),n=G(()=>S.value?"正在连接...":i.value?"已连接，等待数据...":"连接已断开，正在重连..."),D=G(()=>{const l=new Date().toDateString();return s.value.filter(e=>new Date(e.timestamp).toDateString()===l).length}),x=l=>{const e=l.timestamp||new Date().toISOString(),o={id:Date.now().toString()+Math.random().toString(36).substr(2,9),text:l.text,timestamp:e,formattedTime:E(e),isNew:!0,type:l.type};s.value.unshift(o),m.value=e,setTimeout(()=>{o.isNew=!1},2e3),q(()=>{d.value&&(d.value.scrollTop=0)}),g({message:"收到新的识别结果",type:"success",duration:1e3})},O=async()=>{try{await j({title:"确认清空",message:"确定要清空所有监控记录吗？"}),s.value=[],m.value="",g({message:"已清空所有记录",type:"success"})}catch{}},T=async l=>{try{await j({title:"确认删除",message:"确定要删除这条记录吗？"});const e=s.value.findIndex(o=>o.id===l);e>-1&&(s.value.splice(e,1),g({message:"记录已删除",type:"success"}))}catch{}},M=async l=>{try{await navigator.clipboard.writeText(l),g({message:"已复制到剪贴板",type:"success"})}catch(e){console.error("复制失败:",e),g({message:"复制失败",type:"fail"})}},A=()=>{setTimeout(()=>{a.value=!1,g({message:"刷新完成",type:"success"})},1e3)},I=l=>new Date(l).toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),E=l=>{const e=new Date(l),o=e.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),c=e.getMilliseconds().toString().padStart(3,"0");return`${o}.${c}`},W=()=>{C.push("/")};return ae(()=>{_("onMessage",l=>{try{const e=JSON.parse(l.data);x(e)}catch(e){console.error("解析监控消息失败:",e)}}),_("onOpen",()=>{g({message:"监控连接已建立",type:"success"})}),_("onError",()=>{g({message:"监控连接异常",type:"fail"})}),_("onClose",()=>{g({message:"监控连接已断开",type:"fail"})}),N()}),(l,e)=>{const o=b("van-button"),c=b("van-nav-bar"),u=b("van-empty"),P=b("van-tag"),U=b("van-pull-refresh"),V=b("van-grid-item"),K=b("van-grid");return w(),k("div",fe,[p(c,{title:"语音识别监控面板","left-arrow":"",onClickLeft:W,fixed:"",placeholder:""},{right:v(()=>[p(o,{size:"mini",type:"primary",plain:"",onClick:O,disabled:s.value.length===0},{default:v(()=>e[1]||(e[1]=[B(" 清空 ")])),_:1,__:[1]},8,["disabled"])]),_:1}),t("div",ge,[t("div",ye,[t("div",he,[t("div",Ce,[t("div",{class:F(["status-dot",{online:R(i),offline:!R(i),pulse:R(i)}])},null,2),t("span",we,h(n.value),1)]),t("div",be,[t("span",Se," 记录数: "+h(s.value.length),1),m.value?(w(),k("span",Ne," 最后更新: "+h(I(m.value)),1)):H("",!0)])])]),t("div",xe,[p(U,{modelValue:a.value,"onUpdate:modelValue":e[0]||(e[0]=f=>a.value=f),onRefresh:A,"success-text":"刷新成功"},{default:v(()=>[t("div",{class:"records-container",ref_key:"recordsContainer",ref:d},[s.value.length===0?(w(),J(u,{key:0,description:"暂无识别记录",image:"search"},{description:v(()=>e[2]||(e[2]=[t("div",{class:"empty-description"},[t("p",null,"等待语音识别数据..."),t("p",{class:"empty-hint"},"请在主页面进行语音识别")],-1)])),_:1})):(w(),k("div",Oe,[(w(!0),k(le,null,re(s.value,f=>(w(),k("div",{key:f.id,class:F(["record-item",{"new-record":f.isNew}])},[t("div",Te,[t("span",ke,h(f.formattedTime),1),f.type==="control"?(w(),J(P,{key:0,type:"warning"},{default:v(()=>e[3]||(e[3]=[B(" 控制指令 ")])),_:1,__:[3]})):H("",!0)]),t("div",De,h(f.text),1),t("div",Ie,[p(o,{type:"primary",plain:"",onClick:Q=>M(f.text)},{default:v(()=>e[4]||(e[4]=[B(" 复制 ")])),_:2,__:[4]},1032,["onClick"]),p(o,{type:"danger",plain:"",onClick:Q=>T(f.id)},{default:v(()=>e[5]||(e[5]=[B(" 删除 ")])),_:2,__:[5]},1032,["onClick"])])],2))),128))]))],512)]),_:1},8,["modelValue"])])]),t("div",Ee,[p(K,{"column-num":3,border:!1},{default:v(()=>[p(V,null,{default:v(()=>[t("div",Pe,[t("div",Be,h(s.value.length),1),e[6]||(e[6]=t("div",{class:"stat-label"},"总记录数",-1))])]),_:1}),p(V,null,{default:v(()=>[t("div",Re,[t("div",Le,h(D.value),1),e[7]||(e[7]=t("div",{class:"stat-label"},"今日记录",-1))])]),_:1}),p(V,null,{default:v(()=>[t("div",Me,[t("div",Ae,h(R(i)?"在线":"离线"),1),e[8]||(e[8]=t("div",{class:"stat-label"},"连接状态",-1))])]),_:1})]),_:1})])])}}});const $e=ce(We,[["__scopeId","data-v-a9fbae1a"]]);export{$e as default};
