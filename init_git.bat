@echo off
REM Git仓库初始化脚本 (Windows版本)

echo 正在初始化新的Git仓库...

REM 初始化Git仓库
git init

REM 创建.gitignore文件
(
echo # Python缓存文件
echo __pycache__/
echo *.py[cod]
echo *$py.class
echo *.so
echo.
echo # 分发/打包
echo .Python
echo build/
echo develop-eggs/
echo dist/
echo downloads/
echo eggs/
echo .eggs/
echo lib/
echo lib64/
echo parts/
echo sdist/
echo var/
echo wheels/
echo *.egg-info/
echo .installed.cfg
echo *.egg
echo MANIFEST
echo.
echo # PyInstaller
echo *.manifest
echo *.spec
echo.
echo # 单元测试/覆盖率报告
echo htmlcov/
echo .tox/
echo .coverage
echo .coverage.*
echo .cache
echo nosetests.xml
echo coverage.xml
echo *.cover
echo .hypothesis/
echo .pytest_cache/
echo.
echo # 环境变量
echo .env
echo .venv
echo env/
echo venv/
echo ENV/
echo env.bak/
echo venv.bak/
echo.
echo # IDE文件
echo .vscode/
echo .idea/
echo *.swp
echo *.swo
echo *~
echo.
echo # 操作系统文件
echo .DS_Store
echo .DS_Store?
echo ._*
echo .Spotlight-V100
echo .Trashes
echo ehthumbs.db
echo Thumbs.db
echo.
echo # 项目特定文件
echo data/
echo logs/
echo temp/
echo models/*.bin
echo models/*.pt
echo models/*.pth
echo.
echo # SSL证书（敏感信息）
echo ssl/*.pem
echo ssl/*.key
echo ssl/*.crt
echo.
echo # 临时文件
echo *.tmp
echo *.temp
echo *.log
echo.
echo # Node.js (如果有前端构建)
echo node_modules/
echo npm-debug.log*
echo yarn-debug.log*
echo yarn-error.log*
) > .gitignore

REM 添加所有文件到暂存区
git add .

REM 创建初始提交
git commit -m "Initial commit: 通用Web服务框架模板

- 基于FastAPI的Web服务框架
- 支持WebSocket实时通信
- 模块化服务架构
- 完整的配置管理系统
- 内置监控功能
- 支持多种服务类型扩展"

echo.
echo Git仓库初始化完成！
echo.
echo 下一步操作：
echo 1. 添加远程仓库: git remote add origin ^<your-repo-url^>
echo 2. 推送到远程仓库: git push -u origin main
echo.
echo 或者继续本地开发：
echo 1. 查看状态: git status
echo 2. 添加更改: git add .
echo 3. 提交更改: git commit -m "your message"
echo.
pause
