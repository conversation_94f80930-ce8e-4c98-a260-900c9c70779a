#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百度实时语音识别服务

基于百度WebSocket API实现的实时语音识别服务，
集成到通用Web服务框架中。
"""

import asyncio
import json
import logging
import uuid
import websocket
import threading
import time
from typing import Dict, Any, Callable, Optional
from datetime import datetime
from service_template import BaseService
import const

logger = logging.getLogger(__name__)

class BaiduASRService(BaseService):
    """百度实时语音识别服务"""
    
    def __init__(self):
        super().__init__("百度实时语音识别服务")
        self.ws_client = None
        self.is_connected = False
        self.session_id = None
        self.result_callback = None
        self.error_callback = None
        self.connection_callback = None
        
        # 配置参数 - 优先从环境变量读取，否则使用const.py中的默认值
        import os
        self.appid = int(os.getenv('BAIDU_ASR_APPID', const.APPID))
        self.appkey = os.getenv('BAIDU_ASR_APPKEY', const.APPKEY)
        self.dev_pid = int(os.getenv('BAIDU_ASR_DEV_PID', const.DEV_PID))
        self.uri = os.getenv('BAIDU_ASR_URI', const.URI)
        
        # 音频参数
        self.sample_rate = 16000
        self.format = "pcm"
        self.chunk_ms = 160  # 160ms的音频块
        self.chunk_len = int(self.sample_rate * 2 / 1000 * self.chunk_ms)
        
    async def initialize(self) -> bool:
        """初始化百度ASR服务"""
        try:
            logger.info(f"正在初始化{self.service_name}...")
            
            # 检查配置参数
            if not all([self.appid, self.appkey, self.dev_pid, self.uri]):
                logger.error("百度ASR配置参数不完整")
                return False
            
            # 检查依赖
            try:
                import websocket
                logger.info("websocket-client依赖检查通过")
            except ImportError:
                logger.error("缺少websocket-client依赖，请安装: pip install websocket-client")
                return False
            
            self.is_initialized = True
            logger.info(f"{self.service_name}初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"{self.service_name}初始化失败: {str(e)}")
            self.is_initialized = False
            return False
    
    def set_callbacks(self, 
                     result_callback: Optional[Callable] = None,
                     error_callback: Optional[Callable] = None,
                     connection_callback: Optional[Callable] = None):
        """设置回调函数"""
        self.result_callback = result_callback
        self.error_callback = error_callback
        self.connection_callback = connection_callback
    
    def start_recognition_session(self) -> str:
        """开始识别会话"""
        if not self.is_initialized:
            raise Exception("服务未初始化")
        
        self.session_id = str(uuid.uuid4())
        uri = f"{self.uri}?sn={self.session_id}"
        
        logger.info(f"开始识别会话，URI: {uri}")
        
        # 创建WebSocket连接
        self.ws_client = websocket.WebSocketApp(
            uri,
            on_open=self._on_open,
            on_message=self._on_message,
            on_error=self._on_error,
            on_close=self._on_close
        )
        
        # 在新线程中运行WebSocket连接
        def run_websocket():
            self.ws_client.run_forever()
        
        threading.Thread(target=run_websocket, daemon=True).start()
        
        return self.session_id
    
    def send_audio_data(self, audio_data: bytes):
        """发送音频数据"""
        if not self.is_connected or not self.ws_client:
            logger.warning("WebSocket未连接，无法发送音频数据")
            return False
        
        try:
            self.ws_client.send(audio_data, websocket.ABNF.OPCODE_BINARY)
            logger.debug(f"发送音频数据: {len(audio_data)} bytes")
            return True
        except Exception as e:
            logger.error(f"发送音频数据失败: {e}")
            return False
    
    def finish_recognition(self):
        """结束识别"""
        if not self.is_connected or not self.ws_client:
            return
        
        try:
            finish_frame = {
                "type": "FINISH"
            }
            self.ws_client.send(json.dumps(finish_frame), websocket.ABNF.OPCODE_TEXT)
            logger.info("发送结束帧")
        except Exception as e:
            logger.error(f"发送结束帧失败: {e}")
    
    def cancel_recognition(self):
        """取消识别"""
        if not self.is_connected or not self.ws_client:
            return
        
        try:
            cancel_frame = {
                "type": "CANCEL"
            }
            self.ws_client.send(json.dumps(cancel_frame), websocket.ABNF.OPCODE_TEXT)
            logger.info("发送取消帧")
        except Exception as e:
            logger.error(f"发送取消帧失败: {e}")
    
    def close_connection(self):
        """关闭连接"""
        if self.ws_client:
            self.ws_client.close()
            self.is_connected = False
            self.session_id = None
    
    def _on_open(self, ws):
        """WebSocket连接打开回调"""
        logger.info("百度ASR WebSocket连接已建立")
        self.is_connected = True
        
        # 发送开始参数帧
        start_frame = {
            "type": "START",
            "data": {
                "appid": self.appid,
                "appkey": self.appkey,
                "dev_pid": self.dev_pid,
                "cuid": f"web_client_{self.session_id}",
                "sample": self.sample_rate,
                "format": self.format
            }
        }
        
        try:
            ws.send(json.dumps(start_frame), websocket.ABNF.OPCODE_TEXT)
            logger.info("发送开始参数帧")
            
            # 通知连接成功
            if self.connection_callback:
                self.connection_callback({
                    "type": "connected",
                    "session_id": self.session_id,
                    "timestamp": datetime.now().isoformat()
                })
                
        except Exception as e:
            logger.error(f"发送开始参数帧失败: {e}")
    
    def _on_message(self, ws, message):
        """接收识别结果回调"""
        try:
            result = json.loads(message)
            logger.info(f"收到识别结果: {result}")
            
            # 处理识别结果
            if self.result_callback:
                processed_result = {
                    "type": "recognition_result",
                    "session_id": self.session_id,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                }
                self.result_callback(processed_result)
                
        except Exception as e:
            logger.error(f"处理识别结果失败: {e}")
            if self.error_callback:
                self.error_callback({
                    "type": "processing_error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })
    
    def _on_error(self, ws, error):
        """WebSocket错误回调"""
        logger.error(f"百度ASR WebSocket错误: {error}")
        self.is_connected = False
        
        if self.error_callback:
            self.error_callback({
                "type": "websocket_error",
                "error": str(error),
                "timestamp": datetime.now().isoformat()
            })
    
    def _on_close(self, ws, close_status_code=None, close_msg=None):
        """WebSocket关闭回调"""
        logger.info(f"百度ASR WebSocket连接已关闭: {close_status_code}, {close_msg}")
        self.is_connected = False
        
        if self.connection_callback:
            self.connection_callback({
                "type": "disconnected",
                "session_id": self.session_id,
                "close_code": close_status_code,
                "close_msg": close_msg,
                "timestamp": datetime.now().isoformat()
            })
    
    async def process_data(self, data: bytes) -> Dict[str, Any]:
        """处理音频数据（兼容BaseService接口）"""
        try:
            if not self.is_initialized:
                init_result = await self.initialize()
                if not init_result:
                    return {"error": f"{self.service_name}未初始化"}
            
            # 这里可以处理单次音频识别
            # 对于实时识别，主要通过WebSocket接口处理
            return {
                "success": True,
                "message": "请使用WebSocket接口进行实时识别",
                "service": self.service_name,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"{self.service_name}处理失败: {str(e)}")
            return {"error": f"{self.service_name}处理失败: {str(e)}"}
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "service": self.service_name,
            "initialized": self.is_initialized,
            "connected": self.is_connected,
            "session_id": self.session_id,
            "config": {
                "appid": self.appid,
                "dev_pid": self.dev_pid,
                "sample_rate": self.sample_rate,
                "format": self.format
            },
            "status": "运行中" if self.is_initialized else "未初始化"
        }
