from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException, UploadFile, File
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse, HTMLResponse
import uvicorn
import asyncio
import json
from typing import List, Dict
import logging
import websockets
from datetime import datetime
import ssl

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

# 连接管理器，用于存储和广播WebSocket连接
class ConnectionManager:
    def __init__(self):
        # 客户端WebSocket连接
        self.active_connections: List[WebSocket] = []
        # 监控WebSocket连接
        self.monitor_connections: List[WebSocket] = []
        # 存储历史识别结果
        self.history: List[Dict] = []
    
    async def connect(self, websocket: WebSocket, is_monitor: bool = False):
        """连接到WebSocket"""
        await websocket.accept()
        if is_monitor:
            self.monitor_connections.append(websocket)
            # 只发送最新一条记录给监控客户端，然后清空历史记录
            if self.history:
                await websocket.send_json(self.history[-1])
                self.history = []  # 清空历史记录
        else:
            self.active_connections.append(websocket)
    
    def disconnect(self, websocket: WebSocket, is_monitor: bool = False):
        """断开WebSocket连接"""
        if is_monitor:
            if websocket in self.monitor_connections:
                self.monitor_connections.remove(websocket)
        else:
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
    
    async def broadcast_to_monitors(self, data: dict):
        """广播数据到所有监控连接"""
        # 添加时间戳
        if "timestamp" not in data:
            data["timestamp"] = datetime.now().isoformat()
        
        # 保存最新记录
        self.history = [data]
        
        # 广播给所有监控连接
        for connection in self.monitor_connections:
            await connection.send_json(data)
            
        # 等待1秒后清除历史记录
        await asyncio.sleep(1)
        self.history = []  # 清空历史记录
        logger.info("历史记录已清除")

# 创建连接管理器
manager = ConnectionManager()

# FunASR服务配置
class FunASRService:
    def __init__(self):
        self.model = None
        self.is_initialized = False
        
    async def initialize(self):
        """
        初始化FunASR模型
        """
        try:
            # 检查Python环境
            import sys
            logging.info(f"Python版本: {sys.version}")
            logging.info(f"Python路径: {sys.executable}")

            # 检查必要的依赖
            try:
                import modelscope
                logging.info(f"modelscope版本: {modelscope.__version__}")
            except ImportError as e:
                logging.error(f"modelscope未安装: {e}")
                logging.error("请在asr-llm环境中运行: pip install modelscope")
                return False

            try:
                from funasr import AutoModel
                logging.info(f"funasr导入成功")

                # 检查funasr版本
                import funasr
                logging.info(f"funasr版本: {funasr.__version__}")
            except ImportError as e:
                logging.error(f"funasr导入失败: {e}")
                logging.error("请在asr-llm环境中运行: pip install funasr")
                return False

            # 使用SenseVoice模型，支持中英文识别
            logging.info("正在加载FunASR模型...")
            logging.info("模型下载可能需要几分钟时间，请耐心等待...")

            # 尝试初始化模型
            try:
                logging.info("尝试初始化SenseVoiceSmall模型...")
                self.model = AutoModel(
                    model="iic/SenseVoiceSmall",
                    device="cpu"
                )
                logging.info("SenseVoiceSmall模型初始化成功")
            except Exception as e1:
                logging.warning(f"SenseVoiceSmall初始化失败: {e1}")
                logging.info("尝试使用Paraformer模型...")
                try:
                    self.model = AutoModel(
                        model="paraformer-zh",
                        device="cpu"
                    )
                    logging.info("Paraformer模型初始化成功")
                except Exception as e2:
                    logging.error(f"Paraformer模型初始化也失败: {e2}")
                    logging.info("尝试使用最基础的模型...")
                    self.model = AutoModel(
                        model="speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
                        device="cpu"
                    )
                    logging.info("基础模型初始化成功")

            self.is_initialized = True
            logging.info("FunASR模型加载完成")
            return True

        except Exception as e:
            logging.error(f"FunASR模型初始化失败: {str(e)}")
            logging.error(f"错误类型: {type(e).__name__}")
            import traceback
            logging.error(f"详细错误信息: {traceback.format_exc()}")
            self.is_initialized = False
            return False
    
    async def recognize_audio(self, audio_data: bytes) -> dict:
        """
        使用FunASR进行音频识别
        """
        try:
            if not self.is_initialized:
                init_result = await self.initialize()
                if not init_result:
                    return {"error": "FunASR模型未初始化"}
            
            import tempfile
            import os
            
            # 将音频数据保存为临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name
            
            try:
                # 使用FunASR进行识别
                result = self.model.generate(
                    input=temp_path,
                    cache={},
                    language="auto",  # 自动检测语言
                    use_itn=True,
                    batch_size_s=60,
                )
                
                # 提取识别文本
                if result and len(result) > 0 and 'text' in result[0]:
                    text = result[0]['text']
                    return {
                        "success": True,
                        "text": text,
                        "service": "FunASR"
                    }
                else:
                    return {"error": "FunASR识别结果为空"}
                    
            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                    
        except Exception as e:
            logging.error(f"FunASR识别错误: {str(e)}")
            return {"error": f"FunASR识别失败: {str(e)}"}

# 创建FunASR服务实例
funasr_service = FunASRService()

@app.get("/")
async def get():
    """返回前端页面"""
    try:
        logger.info("正在读取前端页面...")
        with open("static/index.html", encoding='utf-8') as f:
            return HTMLResponse(f.read())
    except Exception as e:
        logger.error(f"无法读取前端页面: {str(e)}")
        raise HTTPException(status_code=500, detail=f"无法读取前端页面: {str(e)}")

@app.get("/monitor")
async def get_monitor():
    """返回监控页面"""
    try:
        logger.info("正在读取监控页面...")
        with open("static/monitor.html", encoding='utf-8') as f:
            return HTMLResponse(f.read())
    except Exception as e:
        logger.error(f"无法读取监控页面: {str(e)}")
        raise HTTPException(status_code=500, detail=f"无法读取监控页面: {str(e)}")

@app.get("/api/records")
async def get_records():
    """获取所有语音识别记录"""
    try:
        logger.info("获取语音识别记录")
        return {
            "status": "success",
            "total": len(manager.history),
            "records": manager.history
        }
    except Exception as e:
        logger.error(f"获取记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取记录失败: {str(e)}")

@app.get("/api/records/latest")
async def get_latest_record():
    """获取最新的语音识别记录"""
    try:
        if not manager.history:
            return {
                "status": "success",
                "message": "暂无记录",
                "record": None
            }
        
        latest = manager.history[-1]
        logger.info(f"获取最新记录: {latest.get('text', '')}")
        return {
            "status": "success",
            "record": latest
        }
    except Exception as e:
        logger.error(f"获取最新记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最新记录失败: {str(e)}")

@app.get("/api/records/count/{count}")
async def get_recent_records(count: int):
    """获取最近的N条语音识别记录"""
    try:
        if count <= 0:
            raise HTTPException(status_code=400, detail="记录数量必须大于0")
        
        recent_records = manager.history[-count:] if len(manager.history) >= count else manager.history
        logger.info(f"获取最近{len(recent_records)}条记录")
        return {
            "status": "success",
            "requested_count": count,
            "actual_count": len(recent_records),
            "records": recent_records
        }
    except Exception as e:
        logger.error(f"获取最近记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最近记录失败: {str(e)}")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket处理音频流"""
    await manager.connect(websocket)
    logger.info("WebSocket连接已建立")
    
    try:
        while True:
            # 接收前端发送的识别结果
            logger.info("等待接收前端数据...")
            data = await websocket.receive_json()
            
            # 如果是识别结果
            if 'text' in data and data['text']:
                text = data['text']
                logger.info(f"收到前端识别结果: {text}")
                
                # 广播到监控
                await manager.broadcast_to_monitors({"text": text, "timestamp": datetime.now().isoformat()})
                
                # 返回确认
                await websocket.send_json({"status": "success", "received": text})
            else:
                logger.warning(f"收到无效数据格式: {data}")
                await websocket.send_json({"status": "error", "message": "无效数据格式"})
                
    except WebSocketDisconnect:
        logger.info("WebSocket连接已断开")
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket错误: {str(e)}")
    finally:
        logger.info("WebSocket连接已关闭")
        manager.disconnect(websocket)

@app.websocket("/monitor_ws")
async def monitor_websocket_endpoint(websocket: WebSocket):
    """监控WebSocket"""
    await manager.connect(websocket, is_monitor=True)
    logger.info("监控WebSocket连接已建立")
    
    try:
        # 保持连接直到客户端断开
        while True:
            # 仅接收心跳消息，不处理
            await websocket.receive_text()
    except WebSocketDisconnect:
        logger.info("监控WebSocket连接已断开")
    except Exception as e:
        logger.error(f"监控WebSocket错误: {str(e)}")
    finally:
        logger.info("监控WebSocket连接已关闭")
        manager.disconnect(websocket, is_monitor=True)

# FunASR服务API端点
@app.get("/api/funasr/status")
async def get_funasr_status():
    """
    获取FunASR服务状态
    """
    return {
        "service": "FunASR",
        "initialized": funasr_service.is_initialized,
        "model": "iic/SenseVoiceSmall" if funasr_service.is_initialized else None
    }

@app.post("/api/funasr/recognize")
async def recognize_audio_funasr(
    audio_file: UploadFile = File(...)
):
    """
    FunASR音频识别API
    """
    try:
        # 读取上传的音频文件
        audio_data = await audio_file.read()
        
        # 检查文件大小（限制50MB，本地处理可以更大）
        if len(audio_data) > 50 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="音频文件过大，限制50MB")
        
        logging.info(f"接收到音频文件，大小: {len(audio_data)} bytes")
        
        # 调用FunASR服务
        result = await funasr_service.recognize_audio(audio_data)
        
        logging.info(f"FunASR识别结果: {result}")
        return result
        
    except Exception as e:
        logging.error(f"FunASR识别API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"识别失败: {str(e)}")

@app.post("/api/funasr/initialize")
async def initialize_funasr():
    """
    初始化FunASR模型
    """
    try:
        result = await funasr_service.initialize()
        if result:
            return {"success": True, "message": "FunASR模型初始化成功"}
        else:
            return {"success": False, "message": "FunASR模型初始化失败"}
    except Exception as e:
        logging.error(f"FunASR初始化错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"初始化失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    logger.info("正在启动服务器...")
    
    # 配置HTTPS（必须用于iOS麦克风访问）
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=5004,
        ssl_keyfile="ssl/key.pem",
        ssl_certfile="ssl/cert.pem"
    ) 