from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, UploadFile, File
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse, HTMLResponse
import uvicorn
import asyncio
import json
from typing import List, Dict, Optional
import logging
from datetime import datetime
import ssl
import os
from abc import ABC, abstractmethod

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="通用Web服务框架",
    description="基于FastAPI的通用Web服务框架模板",
    version="1.0.0"
)

# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")

# 抽象服务接口
class BaseService(ABC):
    """基础服务抽象类，用于定义服务接口"""

    @abstractmethod
    async def initialize(self) -> bool:
        """初始化服务"""
        pass

    @abstractmethod
    async def process_data(self, data: bytes) -> dict:
        """处理数据"""
        pass

    @abstractmethod
    def get_status(self) -> dict:
        """获取服务状态"""
        pass

# WebSocket连接管理器
class ConnectionManager:
    """WebSocket连接管理器，支持客户端和监控连接"""

    def __init__(self):
        # 客户端WebSocket连接
        self.active_connections: List[WebSocket] = []
        # 监控WebSocket连接
        self.monitor_connections: List[WebSocket] = []
        # 存储历史数据
        self.history: List[Dict] = []
        # 最大历史记录数
        self.max_history = 100

    async def connect(self, websocket: WebSocket, is_monitor: bool = False):
        """连接到WebSocket"""
        await websocket.accept()
        if is_monitor:
            self.monitor_connections.append(websocket)
            logger.info(f"监控连接已建立，当前监控连接数: {len(self.monitor_connections)}")
            # 发送最新记录给新连接的监控客户端
            if self.history:
                await websocket.send_json(self.history[-1])
        else:
            self.active_connections.append(websocket)
            logger.info(f"客户端连接已建立，当前活跃连接数: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket, is_monitor: bool = False):
        """断开WebSocket连接"""
        if is_monitor:
            if websocket in self.monitor_connections:
                self.monitor_connections.remove(websocket)
                logger.info(f"监控连接已断开，当前监控连接数: {len(self.monitor_connections)}")
        else:
            if websocket in self.active_connections:
                self.active_connections.remove(websocket)
                logger.info(f"客户端连接已断开，当前活跃连接数: {len(self.active_connections)}")

    async def broadcast_to_monitors(self, data: dict):
        """广播数据到所有监控连接"""
        # 添加时间戳
        if "timestamp" not in data:
            data["timestamp"] = datetime.now().isoformat()

        # 保存到历史记录
        self.history.append(data)

        # 限制历史记录数量
        if len(self.history) > self.max_history:
            self.history = self.history[-self.max_history:]

        # 广播给所有监控连接
        disconnected_connections = []
        for connection in self.monitor_connections:
            try:
                await connection.send_json(data)
            except Exception as e:
                logger.error(f"广播到监控连接失败: {e}")
                disconnected_connections.append(connection)

        # 清理断开的连接
        for connection in disconnected_connections:
            self.disconnect(connection, is_monitor=True)

    async def broadcast_to_clients(self, data: dict):
        """广播数据到所有客户端连接"""
        disconnected_connections = []
        for connection in self.active_connections:
            try:
                await connection.send_json(data)
            except Exception as e:
                logger.error(f"广播到客户端失败: {e}")
                disconnected_connections.append(connection)

        # 清理断开的连接
        for connection in disconnected_connections:
            self.disconnect(connection, is_monitor=False)

# 创建连接管理器
manager = ConnectionManager()

# 示例服务实现 - 可以替换为具体的业务服务
class ExampleService(BaseService):
    """示例服务实现，展示如何继承BaseService"""

    def __init__(self):
        self.is_initialized = False
        self.service_name = "示例服务"

    async def initialize(self) -> bool:
        """初始化服务"""
        try:
            logger.info(f"正在初始化{self.service_name}...")
            # 这里可以添加具体的初始化逻辑
            # 例如：加载模型、连接数据库、初始化配置等

            self.is_initialized = True
            logger.info(f"{self.service_name}初始化成功")
            return True

        except Exception as e:
            logger.error(f"{self.service_name}初始化失败: {str(e)}")
            self.is_initialized = False
            return False

    async def process_data(self, data: bytes) -> dict:
        """处理数据"""
        try:
            if not self.is_initialized:
                init_result = await self.initialize()
                if not init_result:
                    return {"error": f"{self.service_name}未初始化"}

            # 这里添加具体的数据处理逻辑
            # 例如：音频识别、图像处理、文本分析等

            # 示例：简单返回数据长度
            result = {
                "success": True,
                "message": f"数据处理成功，数据长度: {len(data)} bytes",
                "service": self.service_name,
                "timestamp": datetime.now().isoformat()
            }

            return result

        except Exception as e:
            logger.error(f"{self.service_name}处理数据失败: {str(e)}")
            return {"error": f"{self.service_name}处理失败: {str(e)}"}

    def get_status(self) -> dict:
        """获取服务状态"""
        return {
            "service": self.service_name,
            "initialized": self.is_initialized,
            "status": "运行中" if self.is_initialized else "未初始化"
        }

# 可选：FunASR服务实现（如果需要语音识别功能）
class FunASRService(BaseService):
    """FunASR语音识别服务实现"""

    def __init__(self):
        self.model = None
        self.is_initialized = False
        self.service_name = "FunASR语音识别服务"

    async def initialize(self) -> bool:
        """初始化FunASR模型"""
        try:
            logger.info(f"正在初始化{self.service_name}...")

            # 检查依赖
            try:
                from funasr import AutoModel
                import torch
                logger.info("FunASR依赖检查通过")
            except ImportError as e:
                logger.error(f"FunASR依赖缺失: {e}")
                logger.error("请安装FunASR: pip install funasr")
                return False

            # 初始化模型
            logger.info("正在加载FunASR模型...")
            self.model = AutoModel(
                model="iic/SenseVoiceSmall",  # 可配置的模型
                device="cpu"  # 可配置的设备
            )

            self.is_initialized = True
            logger.info(f"{self.service_name}初始化成功")
            return True

        except Exception as e:
            logger.error(f"{self.service_name}初始化失败: {str(e)}")
            self.is_initialized = False
            return False

    async def process_data(self, audio_data: bytes) -> dict:
        """处理音频数据进行语音识别"""
        try:
            if not self.is_initialized:
                init_result = await self.initialize()
                if not init_result:
                    return {"error": f"{self.service_name}未初始化"}

            import tempfile
            import os

            # 将音频数据保存为临时文件
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_path = temp_file.name

            try:
                # 使用FunASR进行识别
                result = self.model.generate(
                    input=temp_path,
                    cache={},
                    language="auto",  # 自动检测语言
                    use_itn=True,
                    batch_size_s=60,
                )

                # 提取识别文本
                if result and len(result) > 0 and 'text' in result[0]:
                    text = result[0]['text']
                    return {
                        "success": True,
                        "text": text,
                        "service": self.service_name,
                        "timestamp": datetime.now().isoformat()
                    }
                else:
                    return {"error": "语音识别结果为空"}

            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

        except Exception as e:
            logger.error(f"{self.service_name}处理失败: {str(e)}")
            return {"error": f"{self.service_name}处理失败: {str(e)}"}

    def get_status(self) -> dict:
        """获取服务状态"""
        return {
            "service": self.service_name,
            "initialized": self.is_initialized,
            "model": "iic/SenseVoiceSmall" if self.is_initialized else None,
            "status": "运行中" if self.is_initialized else "未初始化"
        }

# 导入百度ASR服务
try:
    from baidu_asr_service import BaiduASRService
    baidu_asr_available = True
except ImportError as e:
    logger.warning(f"百度ASR服务不可用: {e}")
    baidu_asr_available = False

# 创建服务实例 - 可以根据需要选择不同的服务
# 默认使用示例服务，如需语音识别功能请取消注释相应服务
current_service = ExampleService()
# current_service = FunASRService()  # 取消注释以启用FunASR服务

# 创建百度ASR服务实例（用于实时语音识别）
baidu_asr_service = None
if baidu_asr_available:
    baidu_asr_service = BaiduASRService()
    logger.info("百度ASR服务已加载")

# ==================== 基础路由 ====================

@app.get("/")
async def get_index():
    """返回主页面"""
    try:
        logger.info("正在读取主页面...")
        if os.path.exists("static/index.html"):
            with open("static/index.html", encoding='utf-8') as f:
                return HTMLResponse(f.read())
        else:
            return HTMLResponse("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>通用Web服务框架</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>通用Web服务框架</h1>
                <p>欢迎使用通用Web服务框架！</p>
                <p>请在 static/index.html 中自定义您的前端页面。</p>
            </body>
            </html>
            """)
    except Exception as e:
        logger.error(f"无法读取主页面: {str(e)}")
        raise HTTPException(status_code=500, detail=f"无法读取主页面: {str(e)}")

@app.get("/monitor")
async def get_monitor():
    """返回监控页面"""
    try:
        logger.info("正在读取监控页面...")
        if os.path.exists("static/monitor.html"):
            with open("static/monitor.html", encoding='utf-8') as f:
                return HTMLResponse(f.read())
        else:
            return HTMLResponse("""
            <!DOCTYPE html>
            <html>
            <head>
                <title>服务监控</title>
                <meta charset="utf-8">
            </head>
            <body>
                <h1>服务监控</h1>
                <p>请在 static/monitor.html 中自定义您的监控页面。</p>
            </body>
            </html>
            """)
    except Exception as e:
        logger.error(f"无法读取监控页面: {str(e)}")
        raise HTTPException(status_code=500, detail=f"无法读取监控页面: {str(e)}")

# ==================== 数据记录API ====================

@app.get("/api/records")
async def get_records():
    """获取所有数据处理记录"""
    try:
        logger.info("获取数据处理记录")
        return {
            "status": "success",
            "total": len(manager.history),
            "records": manager.history
        }
    except Exception as e:
        logger.error(f"获取记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取记录失败: {str(e)}")

@app.get("/api/records/latest")
async def get_latest_record():
    """获取最新的数据处理记录"""
    try:
        if not manager.history:
            return {
                "status": "success",
                "message": "暂无记录",
                "record": None
            }

        latest = manager.history[-1]
        logger.info(f"获取最新记录: {latest.get('message', latest.get('text', ''))}")
        return {
            "status": "success",
            "record": latest
        }
    except Exception as e:
        logger.error(f"获取最新记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最新记录失败: {str(e)}")

@app.get("/api/records/count/{count}")
async def get_recent_records(count: int):
    """获取最近的N条数据处理记录"""
    try:
        if count <= 0:
            raise HTTPException(status_code=400, detail="记录数量必须大于0")

        recent_records = manager.history[-count:] if len(manager.history) >= count else manager.history
        logger.info(f"获取最近{len(recent_records)}条记录")
        return {
            "status": "success",
            "requested_count": count,
            "actual_count": len(recent_records),
            "records": recent_records
        }
    except Exception as e:
        logger.error(f"获取最近记录失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取最近记录失败: {str(e)}")

# ==================== WebSocket处理 ====================

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket处理客户端连接"""
    await manager.connect(websocket)
    logger.info("客户端WebSocket连接已建立")

    try:
        while True:
            # 接收前端发送的数据
            logger.info("等待接收客户端数据...")
            data = await websocket.receive_json()

            # 处理不同类型的数据
            if 'type' in data:
                if data['type'] == 'text' and 'content' in data:
                    # 文本数据
                    content = data['content']
                    logger.info(f"收到文本数据: {content}")

                    # 广播到监控
                    await manager.broadcast_to_monitors({
                        "type": "text",
                        "content": content,
                        "timestamp": datetime.now().isoformat()
                    })

                    # 返回确认
                    await websocket.send_json({"status": "success", "received": content})

                elif data['type'] == 'ping':
                    # 心跳检测
                    await websocket.send_json({"type": "pong", "timestamp": datetime.now().isoformat()})

                else:
                    logger.warning(f"未知数据类型: {data['type']}")
                    await websocket.send_json({"status": "error", "message": f"未知数据类型: {data['type']}"})
            else:
                logger.warning(f"收到无效数据格式: {data}")
                await websocket.send_json({"status": "error", "message": "无效数据格式，缺少type字段"})

    except WebSocketDisconnect:
        logger.info("客户端WebSocket连接已断开")
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket错误: {str(e)}")
    finally:
        logger.info("客户端WebSocket连接已关闭")
        manager.disconnect(websocket)

@app.websocket("/monitor_ws")
async def monitor_websocket_endpoint(websocket: WebSocket):
    """监控WebSocket连接"""
    await manager.connect(websocket, is_monitor=True)
    logger.info("监控WebSocket连接已建立")

    try:
        # 保持连接直到客户端断开
        while True:
            # 接收心跳消息或其他监控指令
            message = await websocket.receive_text()
            if message == "ping":
                await websocket.send_text("pong")
    except WebSocketDisconnect:
        logger.info("监控WebSocket连接已断开")
    except Exception as e:
        logger.error(f"监控WebSocket错误: {str(e)}")
    finally:
        logger.info("监控WebSocket连接已关闭")
        manager.disconnect(websocket, is_monitor=True)

# ==================== 百度ASR WebSocket ====================

# 存储活跃的ASR会话
asr_sessions = {}

@app.websocket("/asr_ws")
async def baidu_asr_websocket_endpoint(websocket: WebSocket):
    """百度实时语音识别WebSocket端点"""
    if not baidu_asr_service:
        await websocket.close(code=1000, reason="百度ASR服务不可用")
        return

    await websocket.accept()
    session_id = None

    logger.info("百度ASR WebSocket连接已建立")

    try:
        # 初始化百度ASR服务
        if not baidu_asr_service.is_initialized:
            await baidu_asr_service.initialize()

        # 设置回调函数
        async def result_callback(result):
            """识别结果回调"""
            try:
                await websocket.send_json(result)
                # 同时广播到监控
                await manager.broadcast_to_monitors(result)
            except Exception as e:
                logger.error(f"发送识别结果失败: {e}")

        async def error_callback(error):
            """错误回调"""
            try:
                await websocket.send_json(error)
            except Exception as e:
                logger.error(f"发送错误信息失败: {e}")

        async def connection_callback(conn_info):
            """连接状态回调"""
            try:
                await websocket.send_json(conn_info)
                if conn_info["type"] == "connected":
                    nonlocal session_id
                    session_id = conn_info["session_id"]
                    asr_sessions[session_id] = {
                        "websocket": websocket,
                        "service": baidu_asr_service,
                        "start_time": datetime.now()
                    }
            except Exception as e:
                logger.error(f"发送连接信息失败: {e}")

        # 设置回调
        baidu_asr_service.set_callbacks(
            result_callback=result_callback,
            error_callback=error_callback,
            connection_callback=connection_callback
        )

        # 开始识别会话
        session_id = baidu_asr_service.start_recognition_session()

        # 处理客户端消息
        while True:
            try:
                # 接收消息
                message = await websocket.receive()

                if message["type"] == "websocket.receive":
                    if "bytes" in message:
                        # 音频数据
                        audio_data = message["bytes"]
                        baidu_asr_service.send_audio_data(audio_data)
                        logger.debug(f"转发音频数据: {len(audio_data)} bytes")

                    elif "text" in message:
                        # 控制命令
                        try:
                            command = json.loads(message["text"])
                            if command.get("type") == "finish":
                                baidu_asr_service.finish_recognition()
                                logger.info("收到结束命令")
                            elif command.get("type") == "cancel":
                                baidu_asr_service.cancel_recognition()
                                logger.info("收到取消命令")
                        except json.JSONDecodeError:
                            logger.warning(f"无效的控制命令: {message['text']}")

            except Exception as e:
                logger.error(f"处理客户端消息失败: {e}")
                break

    except WebSocketDisconnect:
        logger.info("百度ASR WebSocket连接已断开")
    except Exception as e:
        logger.error(f"百度ASR WebSocket错误: {str(e)}")
    finally:
        # 清理会话
        if session_id and session_id in asr_sessions:
            del asr_sessions[session_id]

        # 关闭百度ASR连接
        if baidu_asr_service:
            baidu_asr_service.close_connection()

        logger.info("百度ASR WebSocket连接已关闭")

# ==================== 服务API ====================

@app.get("/api/service/status")
async def get_service_status():
    """获取当前服务状态"""
    try:
        status = current_service.get_status()
        return {
            "status": "success",
            "service_info": status
        }
    except Exception as e:
        logger.error(f"获取服务状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取服务状态失败: {str(e)}")

@app.post("/api/service/initialize")
async def initialize_service():
    """初始化当前服务"""
    try:
        result = await current_service.initialize()
        if result:
            return {"success": True, "message": "服务初始化成功"}
        else:
            return {"success": False, "message": "服务初始化失败"}
    except Exception as e:
        logger.error(f"服务初始化错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"初始化失败: {str(e)}")

@app.post("/api/service/process")
async def process_data_api(
    data_file: UploadFile = File(...)
):
    """数据处理API"""
    try:
        # 读取上传的文件
        file_data = await data_file.read()

        # 检查文件大小（限制50MB）
        if len(file_data) > 50 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="文件过大，限制50MB")

        logger.info(f"接收到文件，大小: {len(file_data)} bytes，类型: {data_file.content_type}")

        # 调用当前服务处理数据
        result = await current_service.process_data(file_data)

        # 如果处理成功，广播到监控
        if result.get("success"):
            await manager.broadcast_to_monitors(result)

        logger.info(f"数据处理结果: {result}")
        return result

    except Exception as e:
        logger.error(f"数据处理API错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

# ==================== 百度ASR API ====================

@app.get("/api/baidu_asr/status")
async def get_baidu_asr_status():
    """获取百度ASR服务状态"""
    if not baidu_asr_service:
        return {
            "available": False,
            "message": "百度ASR服务不可用"
        }

    try:
        status = baidu_asr_service.get_status()
        return {
            "available": True,
            "status": status
        }
    except Exception as e:
        logger.error(f"获取百度ASR状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@app.post("/api/baidu_asr/initialize")
async def initialize_baidu_asr():
    """初始化百度ASR服务"""
    if not baidu_asr_service:
        raise HTTPException(status_code=404, detail="百度ASR服务不可用")

    try:
        result = await baidu_asr_service.initialize()
        if result:
            return {"success": True, "message": "百度ASR服务初始化成功"}
        else:
            return {"success": False, "message": "百度ASR服务初始化失败"}
    except Exception as e:
        logger.error(f"百度ASR初始化错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"初始化失败: {str(e)}")

@app.get("/api/baidu_asr/sessions")
async def get_asr_sessions():
    """获取当前活跃的ASR会话"""
    try:
        sessions_info = {}
        for session_id, session_data in asr_sessions.items():
            sessions_info[session_id] = {
                "start_time": session_data["start_time"].isoformat(),
                "duration": (datetime.now() - session_data["start_time"]).total_seconds()
            }

        return {
            "success": True,
            "active_sessions": len(asr_sessions),
            "sessions": sessions_info
        }
    except Exception as e:
        logger.error(f"获取ASR会话信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话信息失败: {str(e)}")

@app.delete("/api/baidu_asr/sessions/{session_id}")
async def close_asr_session(session_id: str):
    """关闭指定的ASR会话"""
    try:
        if session_id not in asr_sessions:
            raise HTTPException(status_code=404, detail="会话不存在")

        session_data = asr_sessions[session_id]

        # 关闭WebSocket连接
        try:
            await session_data["websocket"].close()
        except:
            pass

        # 关闭百度ASR连接
        session_data["service"].close_connection()

        # 删除会话记录
        del asr_sessions[session_id]

        return {
            "success": True,
            "message": f"会话 {session_id} 已关闭"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"关闭ASR会话失败: {e}")
        raise HTTPException(status_code=500, detail=f"关闭会话失败: {str(e)}")

# ==================== 服务器启动配置 ====================

def get_ssl_config():
    """获取SSL配置"""
    ssl_keyfile = "ssl/key.pem"
    ssl_certfile = "ssl/cert.pem"

    # 检查SSL证书文件是否存在
    if os.path.exists(ssl_keyfile) and os.path.exists(ssl_certfile):
        logger.info("找到SSL证书文件，启用HTTPS")
        return ssl_keyfile, ssl_certfile
    else:
        logger.warning("未找到SSL证书文件，将使用HTTP")
        logger.info("如需HTTPS，请将证书文件放置在ssl/目录下：")
        logger.info("  - ssl/key.pem (私钥文件)")
        logger.info("  - ssl/cert.pem (证书文件)")
        return None, None

if __name__ == "__main__":
    import uvicorn

    logger.info("=" * 60)
    logger.info("通用Web服务框架启动中...")
    logger.info("=" * 60)

    # 获取SSL配置
    ssl_keyfile, ssl_certfile = get_ssl_config()

    # 服务器配置
    server_config = {
        "app": app,
        "host": "0.0.0.0",
        "port": 5004,
        "log_level": "info"
    }

    # 如果有SSL证书，启用HTTPS
    if ssl_keyfile and ssl_certfile:
        server_config.update({
            "ssl_keyfile": ssl_keyfile,
            "ssl_certfile": ssl_certfile
        })
        logger.info("服务器将在 https://localhost:5004 启动")
    else:
        logger.info("服务器将在 http://localhost:5004 启动")

    logger.info("按 Ctrl+C 停止服务器")
    logger.info("=" * 60)

    # 启动服务器
    uvicorn.run(**server_config)