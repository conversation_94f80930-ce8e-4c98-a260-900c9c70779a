# 通用Web服务框架依赖包
# 核心框架依赖 - 必需
fastapi==0.109.2
uvicorn==0.27.1
websockets==12.0
requests==2.31.0
python-multipart==0.0.9

# 基础工具包 - 推荐
numpy
python-dotenv  # 环境变量管理

# 百度实时语音识别 - 必需（如果使用百度ASR服务）
websocket-client>=1.6.0  # 百度ASR WebSocket客户端

# 可选依赖 - 根据需要安装
# 语音识别相关 (如果使用FunASR服务)
# funasr==1.0.15
# torch>=1.13.0
# torchaudio>=0.13.0
# soundfile
# librosa
# modelscope

# 图像处理相关 (如果需要图像处理功能)
# Pillow>=9.0.0
# opencv-python>=4.5.0

# 文本处理相关 (如果需要NLP功能)
# transformers>=4.20.0
# jieba>=0.42.1

# 数据库相关 (如果需要数据库功能)
# sqlalchemy>=1.4.0
# alembic>=1.8.0
# psycopg2-binary>=2.9.0  # PostgreSQL
# pymongo>=4.0.0  # MongoDB

# 缓存相关 (如果需要Redis缓存)
# redis>=4.3.0
# aioredis>=2.0.0

# 监控和日志相关 (如果需要高级监控)
# prometheus-client>=0.14.0
# structlog>=22.1.0

# 测试相关 (开发环境)
# pytest>=7.0.0
# pytest-asyncio>=0.19.0
# httpx>=0.23.0