# 项目结构说明

## 📁 目录结构

```
通用Web服务框架/
├── README.md                 # 项目说明文档
├── main.py                   # 主服务文件
├── config.py                 # 配置管理
├── service_template.py       # 服务模板
├── requirements.txt          # 依赖包列表
├── start_server.py          # 服务器启动脚本
├── .env.template            # 环境配置模板
├── init_git.sh              # Git初始化脚本 (Linux/macOS)
├── init_git.bat             # Git初始化脚本 (Windows)
├── PROJECT_STRUCTURE.md     # 项目结构说明 (本文件)
├── static/                  # 静态文件目录
│   ├── index.html          # 主页面
│   ├── monitor.html        # 监控页面
│   └── dist/               # 构建文件 (如果有)
├── ssl/                     # SSL证书目录
│   ├── cert.pem            # SSL证书文件
│   └── key.pem             # SSL私钥文件
├── data/                    # 数据存储目录
├── logs/                    # 日志文件目录
├── temp/                    # 临时文件目录
└── models/                  # 模型文件目录
```

## 📋 核心文件说明

### 🔧 配置文件
- **config.py**: 主配置文件，包含服务器、应用、服务等各种配置
- **.env.template**: 环境变量配置模板，复制为.env使用
- **requirements.txt**: Python依赖包列表

### 🚀 服务文件
- **main.py**: 主服务文件，包含FastAPI应用和路由定义
- **service_template.py**: 服务模板，提供各种服务实现示例
- **start_server.py**: 服务器启动脚本，包含环境检查和启动逻辑

### 📖 文档文件
- **README.md**: 项目主要文档，包含使用指南和API说明
- **PROJECT_STRUCTURE.md**: 项目结构说明文档

### 🔨 工具脚本
- **init_git.sh**: Linux/macOS下的Git仓库初始化脚本
- **init_git.bat**: Windows下的Git仓库初始化脚本

### 🌐 前端文件
- **static/index.html**: 主页面模板
- **static/monitor.html**: 监控页面模板
- **static/dist/**: 前端构建文件目录

### 🔒 安全文件
- **ssl/cert.pem**: SSL证书文件
- **ssl/key.pem**: SSL私钥文件

## 🎯 使用场景

### 1. 快速开始新项目
```bash
# 1. 复制配置文件
cp .env.template .env

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动服务
python start_server.py
```

### 2. 自定义服务开发
```python
# 在service_template.py中创建新服务
class MyService(BaseService):
    # 实现您的服务逻辑
    pass

# 在main.py中使用
current_service = MyService()
```

### 3. 部署到生产环境
```bash
# 1. 配置生产环境变量
export ENVIRONMENT=production
export DEBUG=false

# 2. 使用Gunicorn部署
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 🔄 扩展指南

### 添加新的服务类型
1. 在`service_template.py`中创建新的服务类
2. 继承`BaseService`并实现必要方法
3. 在`config.py`中添加相应配置
4. 在`main.py`中注册新服务

### 添加新的API端点
1. 在`main.py`中添加新的路由函数
2. 使用FastAPI装饰器定义端点
3. 更新API文档

### 自定义前端页面
1. 修改`static/index.html`和`static/monitor.html`
2. 添加CSS和JavaScript文件
3. 配置静态文件路由

## 🛡️ 安全注意事项

### SSL证书
- 生产环境请使用正式的SSL证书
- 开发环境可以使用自签名证书
- 证书文件应妥善保管，不要提交到版本控制

### 环境变量
- 敏感信息应通过环境变量配置
- 不要将.env文件提交到版本控制
- 使用.env.template作为配置模板

### 文件上传
- 限制上传文件大小和类型
- 验证文件内容
- 定期清理临时文件

## 📊 监控和日志

### 日志配置
- 日志文件存储在`logs/`目录
- 可通过`LOG_LEVEL`环境变量调整日志级别
- 支持结构化日志输出

### 监控功能
- 访问`/monitor`查看实时监控
- WebSocket连接状态监控
- 服务处理状态监控

## 🤝 贡献指南

### 代码规范
- 遵循PEP 8 Python代码规范
- 添加适当的注释和文档字符串
- 编写单元测试

### 提交规范
- 使用清晰的提交信息
- 每个提交只包含一个功能或修复
- 更新相关文档

---

**🎉 享受使用通用Web服务框架的开发体验！**
