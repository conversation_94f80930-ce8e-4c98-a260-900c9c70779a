import os
import base64
import requests
import asyncio
from typing import Optional

class ASRServiceOffline:
    """专门处理网页端音频的ASR服务"""
    
    def __init__(self, host: str = "**************", port: int = 10097):
        """初始化ASR服务
        
        Args:
            host: ASR服务器地址
            port: ASR服务器端口
        """
        self.host = host
        self.port = port
        self.url = f"http://{host}:{port}/recognition"
        print(f"[ASR-Offline] 初始化完成, 服务地址: {self.url}")

    async def process_audio(self, audio_data: bytes) -> Optional[str]:
        """处理音频数据
        
        Args:
            audio_data: 音频二进制数据
            
        Returns:
            str: 识别的文本,失败返回None
        """
        max_retries = 3  # 最大重试次数
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                print(f"[ASR-Offline] 开始处理音频数据... (尝试 {retry_count + 1}/{max_retries})")
                print(f"[ASR-Offline] 音频数据大小: {len(audio_data)} bytes")
                
                # 准备请求数据
                files = [
                    (
                        "audio",
                        (
                            "audio.wav",  # 文件名
                            audio_data,   # 音频数据
                            "application/octet-stream"  # MIME类型
                        )
                    )
                ]
                
                # 发送请求
                print("[ASR-Offline] 发送请求到ASR服务器...")
                response = requests.post(self.url, files=files, timeout=10)
                
                # 检查响应状态
                if response.status_code != 200:
                    error_msg = f"HTTP {response.status_code}: {response.text}"
                    print(f"[ASR-Offline] 请求失败: {error_msg}")
                    raise Exception(error_msg)
                    
                # 解析响应
                print("[ASR-Offline] 收到响应,正在解析...")
                result = response.json()
                
                # 直接提取text字段
                text = result.get("text", "").strip()
                if text:
                    print(f"[ASR-Offline] 识别成功: {text}")
                    return text
                else:
                    print("[ASR-Offline] 未识别到文本")
                    raise Exception("未识别到文本")
                    
            except Exception as e:
                retry_count += 1
                print(f"[ASR-Offline] 处理出错 ({retry_count}/{max_retries}): {str(e)}")
                if retry_count >= max_retries:
                    print("[ASR-Offline] 达到最大重试次数，放弃重试")
                    return None
                print(f"[ASR-Offline] 等待1秒后重试...")
                await asyncio.sleep(1)

        return None

    async def process_stream(self, audio_queue):
        """处理音频流
        
        Args:
            audio_queue: 音频数据队列
            
        Yields:
            str: 识别的文本
        """
        while True:
            try:
                # 从队列获取音频数据
                audio_data = await audio_queue.get()
                if audio_data is None:  # 结束标记
                    break
                    
                # 处理音频数据
                text = await self.process_audio(audio_data)
                if text:
                    yield text
                    
            except Exception as e:
                print(f"[ASR-Offline] 处理音频流出错: {str(e)}")
                continue

    async def process_base64_audio(self, base64_audio: str) -> Optional[str]:
        """处理Base64编码的音频数据
        
        Args:
            base64_audio: Base64编码的音频数据
            
        Returns:
            str: 识别的文本,失败返回None
        """
        try:
            # 解码Base64数据
            audio_data = base64.b64decode(base64_audio)
            return await self.process_audio(audio_data)
        except Exception as e:
            print(f"[ASR-Offline] Base64解码失败: {str(e)}")
            return None 