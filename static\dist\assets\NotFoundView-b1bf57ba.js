import{d as _,y as r,b as c,e as p,f as e,i as t,z as o,u as a,B as v,E as n,C as m,h as f,D as l,G as N,_ as V}from"./index-bac5b045.js";const x={class:"not-found-container"},B={class:"not-found-content"},g={class:"actions"},k={name:"NotFoundView"},w=_({...k,setup(y){const d=r(),i=()=>{d.push("/")},u=()=>{d.go(-1)};return(C,s)=>(c(),p("div",x,[e("div",B,[s[2]||(s[2]=e("div",{class:"error-code"},"404",-1)),s[3]||(s[3]=e("h1",null,"页面未找到",-1)),s[4]||(s[4]=e("p",null,"抱歉，您访问的页面不存在或已被移除。",-1)),e("div",g,[t(a(l),{type:"primary",onClick:i},{default:o(()=>[t(a(n),null,{default:o(()=>[t(a(m))]),_:1}),s[0]||(s[0]=f(" 返回首页 "))]),_:1,__:[0]}),t(a(l),{onClick:u},{default:o(()=>[t(a(n),null,{default:o(()=>[t(a(N))]),_:1}),s[1]||(s[1]=f(" 返回上页 "))]),_:1,__:[1]})])]),s[5]||(s[5]=v('<div class="illustration" data-v-5fcfa87f><div class="floating-shapes" data-v-5fcfa87f><div class="shape shape-1" data-v-5fcfa87f></div><div class="shape shape-2" data-v-5fcfa87f></div><div class="shape shape-3" data-v-5fcfa87f></div></div></div>',1))]))}});const E=V(w,[["__scopeId","data-v-5fcfa87f"]]);export{E as default};
