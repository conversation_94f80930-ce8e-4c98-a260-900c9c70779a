<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FunASR语音识别</title>
    <style>
        :root {
            --primary: #3a86ff;
            --secondary: #00f5d4;
            --accent: #8338ec;
            --dark: #0f0e17;
            --text: #fffffe;
            --panel: rgba(15, 23, 42, 0.7);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: var(--dark);
            color: var(--text);
            min-height: 100vh;
            overflow-x: hidden;
            background-image: 
                radial-gradient(circle at 20% 35%, rgba(58, 134, 255, 0.15) 0%, transparent 29%),
                radial-gradient(circle at 70% 65%, rgba(131, 56, 236, 0.1) 0%, transparent 20%);
        }

        .container {
            max-width: 800px;
            padding: 20px;
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            height: 100vh;
        }

        .title {
            text-align: center;
            font-size: 2rem;
            font-weight: 600;
            margin: 20px 0;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .robot-interface {
            background: var(--panel);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .mic-container {
            position: relative;
            margin: 20px 0;
        }

        .mic-button {
            position: relative;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(131, 56, 236, 0.3);
            transition: all 0.3s ease;
            border: none;
            outline: none;
        }

        .mic-button:active {
            transform: scale(0.95);
        }

        .mic-icon {
            width: 40px;
            height: 40px;
            fill: white;
        }

        .mic-pulse {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--accent));
            opacity: 0;
            z-index: -1;
        }

        .recording .mic-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 0.7; }
            70% { transform: scale(1.5); opacity: 0; }
            100% { transform: scale(1); opacity: 0; }
        }

        .mic-hint {
            margin-top: 20px;
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
        }

        .recognition-engine {
            margin-top: 15px;
            font-size: 14px;
            text-align: center;
            padding: 8px 20px;
            border-radius: 20px;
            background: rgba(0, 245, 212, 0.2);
            color: #00f5d4;
            border: 1px solid rgba(0, 245, 212, 0.3);
        }

        .control-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .control-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 24px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            min-width: 100px;
        }

        .continue-btn {
            background: linear-gradient(135deg, #00f5d4, #00d4aa);
            color: white;
        }

        .stop-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .result-panel {
            background: var(--panel);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: 300px;
            overflow-y: auto;
        }

        .result-panel h3 {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 20px;
            color: rgba(255, 255, 255, 0.8);
        }

        .result-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 15px;
            border-left: 3px solid var(--secondary);
        }

        .text-content {
            margin-bottom: 10px;
            font-size: 0.95rem;
        }

        .error {
            color: #ff6b6b;
        }

        .success {
            color: #00f5d4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">FunASR语音识别</h1>
        
        <div class="robot-interface">
            <div class="mic-container" id="micContainer">
                <button class="mic-button" id="recordButton">
                    <svg class="mic-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M12 16c2.206 0 4-1.795 4-4V6c0-2.206-1.794-4-4-4S8 3.794 8 6v6c0 2.205 1.794 4 4 4zm-1-14h2v3h-2V2zm0 5h2v3h-2V7z" fill="currentColor"></path>
                        <path d="M19 12v-2a1 1 0 012 0v2a9 9 0 01-9 9h-.5a9 9 0 01-9-9v-2a1 1 0 012 0v2a7 7 0 007 7h.5a7 7 0 007-7z" fill="currentColor"></path>
                    </svg>
                </button>
                <div class="mic-pulse"></div>
            </div>
            <div class="mic-hint" id="micHint">点击麦克风按钮开始录音，再次点击结束录音</div>
            <div class="recognition-engine" id="recognitionEngine">识别引擎: FunASR</div>

            <div class="control-buttons">
                <button class="control-btn continue-btn" id="continueButton">继续</button>
                <button class="control-btn stop-btn" id="stopButton">停止</button>
            </div>
        </div>
        
        <div class="result-panel">
            <h3>识别历史</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let isRecording = false;
        let ws = null;
        let funASR = {
            enabled: true,
            initialized: false,
            recorder: null,
            recordingData: [],
            isRecording: false
        };

        const recordButton = document.getElementById('recordButton');
        const results = document.getElementById('results');
        const micContainer = document.getElementById('micContainer');
        const micHint = document.getElementById('micHint');
        const recognitionEngine = document.getElementById('recognitionEngine');
        const continueButton = document.getElementById('continueButton');
        const stopButton = document.getElementById('stopButton');

        // 初始化FunASR服务
        async function initFunASR() {
            try {
                console.log("初始化FunASR服务...");
                micHint.textContent = "正在初始化FunASR服务...";

                // 检查FunASR服务状态
                const statusResponse = await fetch('/api/funasr/status');
                const statusData = await statusResponse.json();

                if (!statusData.initialized) {
                    console.log("FunASR模型未初始化，正在初始化...");
                    micHint.textContent = "正在初始化语音识别模型，请稍等...";

                    // 初始化FunASR模型
                    const initResponse = await fetch('/api/funasr/initialize', {
                        method: 'POST'
                    });
                    const initData = await initResponse.json();

                    if (!initData.success) {
                        throw new Error(initData.message || 'FunASR初始化失败');
                    }
                }

                funASR.initialized = true;
                micHint.textContent = "FunASR语音识别服务已就绪";
                micHint.className = "mic-hint success";
                recognitionEngine.textContent = "识别引擎: FunASR (已就绪)";

                console.log("FunASR服务初始化成功");
                return true;

            } catch (error) {
                console.error("FunASR服务初始化失败:", error);
                micHint.textContent = "FunASR初始化失败: " + error.message;
                micHint.className = "mic-hint error";
                recognitionEngine.textContent = "识别引擎: FunASR (初始化失败)";
                return false;
            }
        }

        // 初始化Web Audio录音
        async function initWebAudioRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    }
                });

                const audioContext = new (window.AudioContext || window.webkitAudioContext)({
                    sampleRate: 16000
                });

                const source = audioContext.createMediaStreamSource(stream);
                const processor = audioContext.createScriptProcessor(4096, 1, 1);

                funASR.recordingData = [];

                processor.onaudioprocess = function(e) {
                    if (funASR.isRecording) {
                        const inputData = e.inputBuffer.getChannelData(0);
                        // 转换为16位整数
                        const outputData = new Int16Array(inputData.length);
                        for (let i = 0; i < inputData.length; i++) {
                            outputData[i] = Math.max(-32768, Math.min(32767, inputData[i] * 32768));
                        }
                        funASR.recordingData.push(outputData);
                    }
                };

                source.connect(processor);
                processor.connect(audioContext.destination);

                funASR.recorder = {
                    audioContext,
                    processor,
                    stream
                };

                return true;

            } catch (error) {
                console.error("初始化Web Audio录音失败:", error);
                return false;
            }
        }

        // 开始FunASR录音
        async function startFunASRRecording() {
            try {
                if (!funASR.initialized) {
                    const initResult = await initFunASR();
                    if (!initResult) {
                        throw new Error('FunASR初始化失败');
                    }
                }

                // 初始化录音设备
                if (!funASR.recorder) {
                    const initResult = await initWebAudioRecording();
                    if (!initResult) {
                        throw new Error('录音设备初始化失败');
                    }
                }

                // 开始录音
                funASR.isRecording = true;
                funASR.recordingData = [];
                isRecording = true;

                micContainer.classList.add('recording');
                micHint.textContent = "正在录音中...";
                micHint.className = "mic-hint";

                console.log("FunASR录音已启动");
                return true;

            } catch (error) {
                console.error("FunASR录音启动失败:", error);
                micHint.textContent = "录音启动失败: " + error.message;
                micHint.className = "mic-hint error";
                return false;
            }
        }

        // 停止FunASR录音并识别
        async function stopFunASRRecording() {
            try {
                funASR.isRecording = false;
                isRecording = false;

                micContainer.classList.remove('recording');
                micHint.textContent = "正在处理录音，请稍等...";

                // 合并录音数据
                const totalLength = funASR.recordingData.reduce((acc, chunk) => acc + chunk.length, 0);
                const mergedData = new Int16Array(totalLength);
                let offset = 0;

                for (const chunk of funASR.recordingData) {
                    mergedData.set(chunk, offset);
                    offset += chunk.length;
                }

                // 转换为WAV格式
                const wavBlob = createWavBlob(mergedData, 16000);

                // 上传到服务器进行FunASR识别
                const formData = new FormData();
                formData.append('audio_file', wavBlob, 'recording.wav');

                const response = await fetch('/api/funasr/recognize', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success && result.text) {
                    console.log("FunASR识别结果:", result.text);

                    micHint.textContent = "识别完成";
                    micHint.className = "mic-hint success";

                    // 显示结果
                    displayResult(result.text);

                    // 发送到WebSocket
                    sendToWebSocket(result.text, true);
                } else {
                    throw new Error(result.error || '识别失败');
                }

            } catch (error) {
                console.error("FunASR识别失败:", error);
                micHint.textContent = "识别失败: " + error.message;
                micHint.className = "mic-hint error";
            }
        }

        // 创建WAV格式音频
        function createWavBlob(audioData, sampleRate) {
            const length = audioData.length;
            const buffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(buffer);

            // WAV文件头
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);

            // 音频数据
            let offset = 44;
            for (let i = 0; i < length; i++) {
                view.setInt16(offset, audioData[i], true);
                offset += 2;
            }

            return new Blob([buffer], { type: 'audio/wav' });
        }

        // 切换录音状态
        async function toggleRecording() {
            if (isRecording) {
                await stopFunASRRecording();
            } else {
                await startFunASRRecording();
            }
        }

        // 显示识别结果
        function displayResult(text) {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';

            const textContent = document.createElement('div');
            textContent.className = 'text-content';
            textContent.textContent = text;

            const timestamp = document.createElement('div');
            timestamp.style.fontSize = '12px';
            timestamp.style.color = 'rgba(255, 255, 255, 0.5)';
            timestamp.textContent = new Date().toLocaleTimeString();

            resultItem.appendChild(textContent);
            resultItem.appendChild(timestamp);

            results.insertBefore(resultItem, results.firstChild);
        }

        // 发送到WebSocket
        function sendToWebSocket(text, isFinal = false) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    text: text,
                    timestamp: new Date().toISOString(),
                    isFinal: isFinal
                }));
            }
        }

        // 初始化WebSocket
        function initWebSocket() {
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws`;

                ws = new WebSocket(wsUrl);

                ws.onopen = function() {
                    console.log('WebSocket连接已建立');
                };

                ws.onmessage = function(event) {
                    console.log('收到WebSocket消息:', event.data);
                };

                ws.onclose = function() {
                    console.log('WebSocket连接已关闭');
                    setTimeout(initWebSocket, 3000); // 3秒后重连
                };

                ws.onerror = function(error) {
                    console.error('WebSocket错误:', error);
                };

            } catch (error) {
                console.error('WebSocket初始化失败:', error);
            }
        }

        // 控制按钮功能
        function handleContinue() {
            sendControlCommand("继续");
        }

        function handleStop() {
            sendControlCommand("停止");
        }

        function sendControlCommand(command) {
            console.log("发送控制指令:", command);
            displayResult(command);

            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({
                    text: command,
                    timestamp: new Date().toISOString(),
                    type: 'control'
                }));
            }
        }

        // 事件监听器
        recordButton.addEventListener('click', toggleRecording);
        continueButton.addEventListener('click', handleContinue);
        stopButton.addEventListener('click', handleStop);

        // 页面加载时初始化
        window.onload = async function() {
            console.log("页面加载完成，初始化...");

            // 初始化WebSocket
            initWebSocket();

            // 初始化FunASR
            await initFunASR();

            // 预先请求麦克风权限
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                console.log("麦克风权限获取成功");
                stream.getTracks().forEach(track => track.stop());
                micHint.textContent = "FunASR语音识别服务已就绪，点击按钮开始录音";
            } catch (err) {
                console.error("麦克风权限获取失败:", err);
                micHint.textContent = "无法访问麦克风: " + err.message;
                micHint.className = "mic-hint error";
            }
        };
    </script>
</body>
</html>
