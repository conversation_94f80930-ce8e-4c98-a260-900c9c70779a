import{g as E,A as N}from"./api-448a60d8.js";import{d as B,r as w,c as k,o as G,a as j,b as _,e as h,f as d,n as D,u as W,g as $,t as O,h as T,s as y,_ as P,i as U,F as J,j as q,k as K}from"./index-bac5b045.js";var u=(m=>(m.IDLE="idle",m.INITIALIZING="initializing",m.READY="ready",m.RECORDING="recording",m.PROCESSING="processing",m.ERROR="error",m))(u||{});class Q{createWavBlob(i,v){const o=i.length,n=new ArrayBuffer(44+o*2),e=new DataView(n),g=(s,a)=>{for(let r=0;r<a.length;r++)e.setUint8(s+r,a.charCodeAt(r))};g(0,"RIFF"),e.setUint32(4,36+o*2,!0),g(8,"WAVE"),g(12,"fmt "),e.setUint32(16,16,!0),e.setUint16(20,1,!0),e.setUint16(22,1,!0),e.setUint32(24,v,!0),e.setUint32(28,v*2,!0),e.setUint16(32,2,!0),e.setUint16(34,16,!0),g(36,"data"),e.setUint32(40,o*2,!0);let l=44;for(let s=0;s<o;s++)e.setInt16(l,i[s],!0),l+=2;return new Blob([n],{type:"audio/wav"})}mergeAudioData(i){const v=i.reduce((e,g)=>e+g.length,0),o=new Int16Array(v);let n=0;for(const e of i)o.set(e,n),n+=e.length;return o}convertToInt16(i){const v=new Int16Array(i.length);for(let o=0;o<i.length;o++)v[o]=Math.max(-32768,Math.min(32767,i[o]*32768));return v}static checkAudioSupport(){return{webAudio:!!(window.AudioContext||window.webkitAudioContext),mediaDevices:!!navigator.mediaDevices,getUserMedia:!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia)}}static createAudioContext(i=16e3){const v=window.AudioContext||window.webkitAudioContext;return new v({sampleRate:i})}}const x=new Q,X={class:"voice-recorder"},ee={class:"recorder-interface"},te=["disabled"],oe={key:0,class:"mic-pulse"},se={key:0,class:"control-buttons"},ne=["disabled"],ae=["disabled"],re=B({__name:"VoiceRecorder",props:{autoInit:{type:Boolean,default:!0},showControls:{type:Boolean,default:!0},onResult:{},onError:{}},emits:["result","error","statusChange","controlCommand"],setup(m,{emit:i}){const v=m,o=i,n=w(u.IDLE),e=w(!1),g=w(!1),l=w({enabled:!0,initialized:!1,recorder:null,recordingData:[],isRecording:!1}),s=w(null),a=w(!1),r={sampleRate:16e3,channelCount:1,echoCancellation:!0,noiseSuppression:!0},I=k(()=>{switch(n.value){case u.IDLE:return"点击麦克风按钮开始录音";case u.INITIALIZING:return"正在初始化语音识别服务...";case u.READY:return"点击麦克风按钮开始录音，再次点击结束录音";case u.RECORDING:return"正在录音中...";case u.PROCESSING:return"正在处理录音，请稍等...";case u.ERROR:return"服务异常，请重试";default:return""}}),S=k(()=>{switch(n.value){case u.READY:return"success";case u.ERROR:return"error";default:return""}}),C=async()=>{try{if(n.value=u.INITIALIZING,o("statusChange",n.value),!(await(await fetch(E(N.ENDPOINTS.FUNASR_STATUS))).json()).initialized){const f=await(await fetch(E(N.ENDPOINTS.FUNASR_INITIALIZE),{method:"POST"})).json();if(!f.success)throw new Error(f.message||"FunASR初始化失败")}return g.value=!0,n.value=u.READY,o("statusChange",n.value),!0}catch(t){return console.error("FunASR服务初始化失败:",t),n.value=u.ERROR,o("statusChange",n.value),o("error",t),!1}},F=async()=>{try{const t=await navigator.mediaDevices.getUserMedia({audio:r}),c=new(window.AudioContext||window.webkitAudioContext)({sampleRate:r.sampleRate}),p=c.createMediaStreamSource(t),f=c.createScriptProcessor(4096,1,1);return l.value.recordingData=[],f.onaudioprocess=R=>{if(l.value.isRecording){const A=R.inputBuffer.getChannelData(0),Y=x.convertToInt16(A);l.value.recordingData.push(Y)}},p.connect(f),f.connect(c.destination),l.value.recorder={audioContext:c,processor:f,stream:t},l.value.initialized=!0,!0}catch(t){return console.error("初始化Web Audio录音失败:",t),o("error",t),!1}},L=async()=>{try{return!g.value&&!await C()||!l.value.initialized&&!await F()?!1:(l.value.isRecording=!0,l.value.recordingData=[],e.value=!0,n.value=u.RECORDING,o("statusChange",n.value),!0)}catch(t){return console.error("录音启动失败:",t),o("error",t),!1}},M=async()=>{try{l.value.isRecording=!1,e.value=!1,n.value=u.PROCESSING,o("statusChange",n.value);const t=x.mergeAudioData(l.value.recordingData),c=x.createWavBlob(t,r.sampleRate),p=new FormData;p.append("audio_file",c,"recording.wav");const R=await(await fetch(E(N.ENDPOINTS.FUNASR_RECOGNIZE),{method:"POST",body:p})).json();if(R.success&&R.text){const A={id:Date.now().toString(),text:R.text,timestamp:new Date().toISOString(),confidence:R.confidence,type:"recognition"};n.value=u.READY,o("statusChange",n.value),o("result",A),b(R.text,!0,"recognition"),y({message:"识别完成",type:"success"})}else throw new Error(R.error||"识别失败")}catch(t){console.error("语音识别失败:",t),n.value=u.ERROR,o("statusChange",n.value),o("error",t),y({message:"识别失败",type:"fail"})}},z=async()=>{e.value?await M():await L()},V=()=>{try{const c=`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}/ws`;s.value=new WebSocket(c),s.value.onopen=()=>{console.log("WebSocket连接已建立"),a.value=!0},s.value.onmessage=p=>{console.log("收到WebSocket消息:",p.data)},s.value.onclose=()=>{console.log("WebSocket连接已关闭"),a.value=!1,setTimeout(V,3e3)},s.value.onerror=p=>{console.error("WebSocket错误:",p),a.value=!1}}catch(t){console.error("WebSocket初始化失败:",t)}},b=(t,c=!1,p="recognition")=>{if(s.value&&s.value.readyState===WebSocket.OPEN){const f={text:t,timestamp:new Date().toISOString(),isFinal:c,type:p};s.value.send(JSON.stringify(f))}},Z=()=>{const t="继续";o("controlCommand",t),b(t,!0,"control")},H=()=>{const t="停止";o("controlCommand",t),b(t,!0,"control")};return G(async()=>{V(),v.autoInit&&await C()}),j(()=>{var t;(t=l.value.recorder)!=null&&t.stream&&l.value.recorder.stream.getTracks().forEach(c=>c.stop()),s.value&&s.value.close()}),(t,c)=>(_(),h("div",X,[d("div",ee,[d("div",{class:D(["mic-container",{recording:e.value}])},[d("button",{class:D(["mic-button",{recording:e.value}]),onClick:z,disabled:n.value===W(u).INITIALIZING||n.value===W(u).PROCESSING},c[0]||(c[0]=[d("svg",{class:"mic-icon",viewBox:"0 0 24 24",fill:"currentColor"},[d("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),d("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"})],-1)]),10,te),e.value?(_(),h("div",oe)):$("",!0)],2),d("div",{class:D(["status-hint",S.value])},O(I.value),3),t.showControls?(_(),h("div",se,[d("button",{class:"control-btn continue-btn",onClick:Z,disabled:e.value},c[1]||(c[1]=[d("span",null,"▶",-1),T(" 继续 ")]),8,ne),d("button",{class:"control-btn stop-btn",onClick:H,disabled:e.value},c[2]||(c[2]=[d("span",null,"⏹",-1),T(" 停止 ")]),8,ae)])):$("",!0)])]))}});const ce=P(re,[["__scopeId","data-v-cd889149"]]),ie={class:"recognition-history"},le={class:"history-header"},ue={class:"history-content"},de={key:0,class:"no-records"},ve={key:1,class:"records-list"},me={class:"record-content"},ge={class:"text-content"},pe={class:"timestamp"},fe=B({__name:"RecognitionHistory",props:{maxRecords:{default:50},autoScroll:{type:Boolean,default:!0},showTimestamp:{type:Boolean,default:!0}},emits:["clear","recordClick"],setup(m,{expose:i,emit:v}){const o=m,n=v,e=w([]),g=k(()=>e.value.slice(0,o.maxRecords)),l=r=>{e.value.unshift(r),e.value.length>o.maxRecords&&(e.value=e.value.slice(0,o.maxRecords))},s=()=>{e.value=[],n("clear")},a=r=>`${new Date(r).toLocaleTimeString()}`;return i({addRecord:l,clearRecords:s,records:g}),(r,I)=>{const S=K("van-icon");return _(),h("div",ie,[d("div",le,[d("h3",null,[U(S,{name:"eye-o"}),I[0]||(I[0]=T(" 识别历史 "))])]),d("div",ue,[e.value.length===0?(_(),h("div",de," 暂无识别记录 ")):(_(),h("div",ve,[(_(!0),h(J,null,q(e.value,C=>(_(),h("div",{key:C.id,class:D(["record-item",{"control-command":C.type==="control"}])},[d("div",me,[d("div",ge,O(C.text),1),d("div",pe,O(a(C.timestamp)),1)])],2))),128))]))])])}}});const _e=P(fe,[["__scopeId","data-v-f555ed3d"]]),he={class:"voice-recognition-page"},Re={class:"page-content"},we=B({__name:"VoiceRecognition",setup(m){const i=w(),v=s=>{var a;(a=i.value)==null||a.addRecord(s),y({message:"识别完成",type:"success"})},o=s=>{console.error("语音识别错误:",s);let a=s.message,r=3e3;a.includes("权限被拒绝")?r=5e3:a.includes("未检测到语音")&&(r=2e3),y({message:a,type:"fail",duration:r})},n=s=>{var r;console.log("控制命令:",s);const a={id:Date.now().toString(),text:s,timestamp:new Date().toISOString(),type:"control"};(r=i.value)==null||r.addRecord(a)},e=()=>{y({message:"已清空记录",type:"success"})},g=s=>{console.log("点击记录:",s)},l=()=>{const s="webkitSpeechRecognition"in window||"SpeechRecognition"in window,a=navigator.mediaDevices&&navigator.mediaDevices.getUserMedia;return!s||!a?(y({message:"您的浏览器不支持语音识别功能，建议使用Chrome、Edge或Safari浏览器",type:"fail",duration:5e3}),!1):!0};return G(()=>{l()}),(s,a)=>(_(),h("div",he,[d("div",Re,[U(ce,{onResult:v,onError:o,onControlCommand:n}),U(_e,{ref_key:"historyRef",ref:i,onClear:e,onRecordClick:g},null,512)])]))}});const Ie=P(we,[["__scopeId","data-v-57e784b6"]]);export{Ie as default};
